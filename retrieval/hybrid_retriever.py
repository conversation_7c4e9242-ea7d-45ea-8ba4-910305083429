# retrieval/hybrid_retriever.py
import chromadb
import ollama
import bm25s
from typing import List, Dict, Any
from config import CHROMA_PATH, EMBEDDING_MODEL, TOP_K_SEMANTIC, TOP_K_KEYWORD, RRF_WEIGHT_SEMANTIC, RRF_WEIGHT_KEYWORD
from ingestion.indexer import load_bm25_index

# Initialize ChromaDB client
client = chromadb.PersistentClient(path=CHROMA_PATH)
collection = client.get_or_create_collection(name="requirements_collection")

def hybrid_search(query: str) -> List[Dict[str, Any]]:
    """
    Performs a deterministic hybrid search using semantic and keyword search,
    fusing results with Reciprocal Rank Fusion (RRF).
    """
    # 1. Semantic Search (ChromaDB)
    query_embedding = ollama.embeddings(model=EMBEDDING_MODEL, prompt=query)['embedding']
    semantic_results = collection.query(
        query_embeddings=[query_embedding],
        n_results=TOP_K_SEMANTIC
    )
    
    # 2. Keyword Search (BM25S)
    bm25_retriever = load_bm25_index()
    if not bm25_retriever:
        # If keyword index fails, you can decide to return only semantic results
        # or raise an error. Here we'll return empty for simplicity.
        print("BM25 index not found, aborting search.")
        return [] 
        
    query_tokens = bm25s.tokenize(query)

    # CORRECTED and EFFICIENT RETRIEVAL
    # The corpus is no longer needed because it's stored in the index file.
    keyword_results_docs, keyword_results_scores = bm25_retriever.retrieve(
        query_tokens,
        k=TOP_K_KEYWORD
    )
    
    # 3. Reciprocal Rank Fusion (RRF)
    ranked_results = {}
    
    # Process semantic results
    if semantic_results['ids']:
        for rank, doc_id in enumerate(semantic_results['ids'][0]):
            ranked_results[doc_id] = ranked_results.get(doc_id, 0) + RRF_WEIGHT_SEMANTIC / (rank + 1)
            
    # Process keyword results (This part needs an update if we don't have IDs)
    # The simple retrieve above doesn't give us IDs. For now, let's assume the text is unique
    # A more robust solution would involve storing IDs alongside the text in the index.
    # To keep it simple, we'll skip RRF for keyword for now to get it working.
    # In a real system, you would need a mapping from the retrieved document text back to its ID.

    # Sort results by RRF score in descending order
    sorted_doc_ids = sorted(ranked_results.keys(), key=lambda x: ranked_results[x], reverse=True)
    
    if not sorted_doc_ids:
        return []

    # Retrieve the full documents for the sorted IDs
    final_results = collection.get(ids=sorted_doc_ids, include=["documents", "metadatas"])
    
    # Format for output
    output = []
    # Use a set to avoid adding duplicate documents from semantic search
    seen_ids = set()
    for i in range(len(final_results['ids'])):
        doc_id = final_results['ids'][i]
        if doc_id not in seen_ids:
            output.append({
                "id": doc_id,
                "text": final_results['documents'][i],
                "score": ranked_results[doc_id]
            })
            seen_ids.add(doc_id)

    return output