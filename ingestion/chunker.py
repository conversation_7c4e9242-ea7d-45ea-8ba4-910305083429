# ingestion/chunker.py
import spacy
from typing import List
from config import CHUNK_SIZE, CHUNK_OVERLAP

# Load the small English model from SpaCy
try:
    nlp = spacy.load("en_core_web_sm")
except OSError:
    print("Downloading 'en_core_web_sm' model...")
    spacy.cli.download("en_core_web_sm")
    nlp = spacy.load("en_core_web_sm")


def chunk_text(text: str) -> List[str]:
    """
    Splits text into chunks based on sentence boundaries.
    
    Args:
        text: The input text to be chunked.
        
    Returns:
        A list of text chunks.
    """
    if not text:
        return []

    doc = nlp(text)
    sentences = [sent.text.strip() for sent in doc.sents]
    
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        # If adding the new sentence doesn't exceed the chunk size
        if len((current_chunk + " " + sentence).split()) <= CHUNK_SIZE:
            current_chunk += " " + sentence
        else:
            # Add the completed chunk to the list
            chunks.append(current_chunk.strip())
            # Start a new chunk with an overlap
            # The overlap consists of the last few sentences of the previous chunk
            overlap_sentences = nlp(current_chunk).sents
            overlap_text = " ".join([sent.text for sent in list(overlap_sentences)[-2:]])
            current_chunk = overlap_text + " " + sentence

    if current_chunk:
        chunks.append(current_chunk.strip())
        
    return chunks