# ingestion/indexer.py
import chromadb
import bm25s
import ollama
import os
import pickle  # <-- ADD THIS IMPORT
from typing import List
from config import CHROMA_PATH, BM25_PATH, EMBEDDING_MODEL
from ingestion.database import get_db_connection

# Initialize ChromaDB client
client = chromadb.PersistentClient(path=CHROMA_PATH)
collection = client.get_or_create_collection(name="requirements_collection")

def get_embeddings(texts: List[str]) -> List[List[float]]:
    if not texts:
        return []
    print(f"Generating embeddings for {len(texts)} texts...")
    embeddings = []
    for text in texts:
        try:
            response = ollama.embeddings(model=EMBEDDING_MODEL, prompt=text)
            embeddings.append(response['embedding'])
        except Exception as e:
            print(f"Error getting embedding for text chunk: {e}")
    return embeddings

def index_chunks(chunk_ids: List[str], chunks: List[str]):
    if chunks:
        print("Indexing new chunks in ChromaDB...")
        embeddings = get_embeddings(chunks)
        if embeddings:
            collection.add(
                embeddings=embeddings,
                documents=chunks,
                ids=chunk_ids
            )
            print(f"Indexed {len(chunks)} new chunks in ChromaDB.")
        else:
            print("Skipping ChromaDB indexing due to embedding generation failure.")

    print("\nRebuilding BM25S keyword index from the full database...")
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT chunk_text FROM chunks ORDER BY chunk_id")
    all_chunks_data = cursor.fetchall()
    conn.close()

    if not all_chunks_data:
        print("No chunks found in the database. Skipping BM25S indexing.")
        return

    full_corpus = [row['chunk_text'] for row in all_chunks_data]
    retriever = bm25s.BM25()
    print("Tokenizing and indexing full corpus in BM25S...")
    corpus_tokens = [doc.split() for doc in full_corpus]
    retriever.index(corpus_tokens, docs=full_corpus) # Store docs in the index

    # CORRECTED SAVE METHOD
    with open(BM25_PATH, "wb") as f:
        pickle.dump(retriever, f)
    
    print(f"BM25S index rebuilt and saved to {BM25_PATH} with {len(full_corpus)} total chunks.")


def load_bm25_index():
    if not os.path.exists(BM25_PATH):
        print("BM25S index file not found. Please ingest a document first.")
        return None
    try:
        # CORRECTED LOAD METHOD
        with open(BM25_PATH, "rb") as f:
            bm25_retriever = pickle.load(f)
        return bm25_retriever
    except Exception as e:
        print(f"Error loading BM25S index: {e}")
        return None