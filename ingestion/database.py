# ingestion/database.py
import sqlite3
import os
from config import DB_PATH

def get_db_connection():
    """Establishes and returns a connection to the SQLite database."""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def initialize_database():
    """Creates the necessary tables if they don't already exist."""
    if os.path.exists(DB_PATH):
        print("Database already exists.")
        return

    print("Initializing new database...")
    conn = get_db_connection()
    cursor = conn.cursor()

    # Documents table to store info about each ingested file
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS documents (
            doc_id INTEGER PRIMARY KEY AUTOINCREMENT,
            filename TEXT NOT NULL,
            hash TEXT NOT NULL UNIQUE,
            ingested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Chunks table to store text chunks from documents
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS chunks (
            chunk_id INTEGER PRIMARY KEY AUTOINCREMENT,
            doc_id INTEGER,
            chunk_text TEXT NOT NULL,
            page_number INTEGER,
            section TEXT,
            FOREIGN KEY(doc_id) REFERENCES documents(doc_id)
        )
    ''')
    conn.commit()
    conn.close()
    print("Database initialized successfully.")

if __name__ == '__main__':
    initialize_database()