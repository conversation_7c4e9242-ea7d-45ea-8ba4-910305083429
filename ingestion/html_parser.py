# ingestion/html_parser.py
from bs4 import BeautifulSoup

def parse_html(file_path: str) -> str:
    """
    Parses an HTML file and extracts clean text content.
    
    Args:
        file_path: The path to the HTML file.
        
    Returns:
        A string containing the extracted text.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')
        
        # Remove script and style elements
        for script_or_style in soup(['script', 'style']):
            script_or_style.decompose()
            
        # Get text, strip leading/trailing whitespace, and join lines
        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = '\n'.join(chunk for chunk in chunks if chunk)
        
        return text
    except Exception as e:
        print(f"Error parsing HTML file {file_path}: {e}")
        return ""