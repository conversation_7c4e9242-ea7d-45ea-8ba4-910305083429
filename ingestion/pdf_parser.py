# ingestion/pdf_parser.py
import fitz  # PyMuPDF

def parse_pdf(file_path: str) -> str:
    """
    Parses a PDF file and extracts clean text content from all pages.
    
    Args:
        file_path: The path to the PDF file.
        
    Returns:
        A string containing the extracted text.
    """
    try:
        # Use a 'with' statement to ensure the document is always closed.
        with fitz.open(file_path) as doc:
            # Use a list comprehension for a more concise and efficient loop.
            full_text = [page.get_text() for page in doc]
        
        return "\n".join(full_text)
    except Exception as e:
        # The error handling is good for logging issues.
        print(f"Error parsing PDF file {file_path}: {e}")
        return ""