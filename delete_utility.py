# delete_utility.py
import sqlite3
import chromadb
import os
import sys
from config import DB_PATH, CHROMA_PATH, BM25_PATH

def delete_document_by_filename(filename: str):
    """
    Completely removes a document and all its associated data from SQLite,
    ChromaDB, and deletes the BM25 index for rebuilding.
    """
    print(f"--- Starting deletion process for: {filename} ---")

    # --- 1. Connect to Databases ---
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    client = chromadb.PersistentClient(path=CHROMA_PATH)
    # Use get_collection to avoid errors if the collection doesn't exist yet
    try:
        collection = client.get_collection(name="requirements_collection")
    except ValueError:
        print("ChromaDB collection not found. Skipping ChromaDB deletion.")
        collection = None

    try:
        # --- 2. Find the Document ID from SQLite ---
        cursor.execute("SELECT doc_id FROM documents WHERE filename = ?", (filename,))
        result = cursor.fetchone()

        if not result:
            print(f"Error: No document found with filename '{filename}' in the database.")
            return

        doc_id = result[0]
        print(f"Found document in SQLite with doc_id: {doc_id}")

        # --- 3. Get all associated Chunk IDs before deleting them ---
        cursor.execute("SELECT chunk_id FROM chunks WHERE doc_id = ?", (doc_id,))
        chunk_results = cursor.fetchall()
        # The IDs in ChromaDB are stored as f"chunk_{chunk_id}"
        chroma_ids_to_delete = [f"chunk_{row[0]}" for row in chunk_results]

        # --- 4. Delete from ChromaDB ---
        if collection and chroma_ids_to_delete:
            print(f"Deleting {len(chroma_ids_to_delete)} chunks from ChromaDB...")
            collection.delete(ids=chroma_ids_to_delete)
            print("Deletion from ChromaDB complete.")
        else:
            print("No chunks to delete from ChromaDB.")

        # --- 5. Delete from SQLite ---
        # Delete from the 'chunks' table first due to foreign key constraints
        print(f"Deleting chunks for doc_id {doc_id} from SQLite...")
        cursor.execute("DELETE FROM chunks WHERE doc_id = ?", (doc_id,))

        # Then delete from the 'documents' table
        print(f"Deleting document record for doc_id {doc_id} from SQLite...")
        cursor.execute("DELETE FROM documents WHERE doc_id = ?", (doc_id,))

        conn.commit()
        print("Deletion from SQLite complete.")

        # --- 6. Delete the BM25 Index File ---
        if os.path.exists(BM25_PATH):
            print("Deleting BM25 keyword index. It will be rebuilt on the next ingestion.")
            os.remove(BM25_PATH)
            print("BM25 index deleted.")
        else:
            print("BM25 index not found, skipping.")

        print(f"\n✅ Successfully deleted all records for '{filename}'.")

    except Exception as e:
        print(f"An error occurred: {e}")
        conn.rollback() # Roll back any partial database changes
    finally:
        conn.close()


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python delete_utility.py \"<filename_to_delete>\"")
        sys.exit(1)

    document_filename = sys.argv[1]
    delete_document_by_filename(document_filename)