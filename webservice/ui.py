# webservice/ui.py
import gradio as gr
import requests
import json

API_URL = "http://127.0.0.1:8000"

def ingest_document(file):
    if file is None:
        return "Please upload a file first."
    # --- CHANGE: The file object from Gradio is already what we need ---
    # We just need to pass its name and content to the request
    files = {'file': (os.path.basename(file.name), open(file.name, 'rb'))}
    # --- END CHANGE ---
    try:
        response = requests.post(f"{API_URL}/ingest", files=files)
        response.raise_for_status()
        return response.json()['message']
    except requests.exceptions.RequestException as e:
        return f"Error connecting to API: {e}"

def generate_specification_ui(query_text):
    if not query_text:
        return "Please enter a requirement query.", "", ""
    
    payload = {"query": query_text}
    
    try:
        response = requests.post(f"{API_URL}/generate_specification", json=payload)
        response.raise_for_status()
        result = response.json()
        
        spec_json = json.dumps(result.get("generated_specification", {}), indent=2)
        validation_json = json.dumps(result.get("validation", {}), indent=2)
        
        return f"Retrieved {result['retrieved_context_count']} context chunks.", spec_json, validation_json

    except requests.exceptions.RequestException as e:
        return f"Error generating specification: {e}", "", ""

with gr.Blocks(theme=gr.themes.Soft(), title="Customer Requirement Agent") as demo:
    gr.Markdown("# 🚂 Automated Railway Specification Agent")
    gr.Markdown("Based on the deterministic architecture project. Follow the steps below.")
    
    with gr.Row():
        with gr.Column(scale=1):
            gr.Markdown("### Step 1: Ingest Document")
            # --- CHANGE: Update file_types to include PDF ---
            file_input = gr.File(
                label="Upload Requirement File (PDF or HTML)",
                file_types=['.pdf', '.html', '.htm']
            )
            # --- END CHANGE ---
            ingest_button = gr.Button("Ingest Document", variant="secondary")
            ingest_output = gr.Textbox(label="Ingestion Status", interactive=False)
        
        with gr.Column(scale=2):
            gr.Markdown("### Step 2: Generate Specification")
            query_input = gr.Textbox(label="Enter a Specific Requirement Query", placeholder="e.g., The system shall achieve SIL 2 under fault conditions.")
            generate_button = gr.Button("Generate Specification", variant="primary")
            status_output = gr.Textbox(label="Generation Status", interactive=False)
            
            with gr.Accordion("View Results", open=True):
                spec_output = gr.JSON(label="Generated Specification (JSON)")
                validation_output = gr.JSON(label="Validation Result (JSON)")

    ingest_button.click(ingest_document, inputs=file_input, outputs=ingest_output)
    generate_button.click(
        generate_specification_ui, 
        inputs=query_input, 
        outputs=[status_output, spec_output, validation_output]
    )

if __name__ == "__main__":
    # --- CHANGE: Add import for os ---
    import os
    # --- END CHANGE ---
    demo.launch()