# webservice/main.py
from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException
import hashlib
import os
from typing import Dict, Any

from config import DATA_DIR
from ingestion.database import get_db_connection, initialize_database
# --- CHANGE: Import the new PDF parser ---
from ingestion.html_parser import parse_html
from ingestion.pdf_parser import parse_pdf

# --- END CHANGE ---
from ingestion.chunker import chunk_text
from ingestion.indexer import index_chunks
from retrieval.hybrid_retriever import hybrid_search
from generation.generator import generate_specification
from generation.validator import validate_specification

app = FastAPI(title="Customer Requirement Agent")

@app.on_event("startup")
def on_startup():
    initialize_database()

@app.post("/ingest", summary="Ingest and process a requirement document")
async def ingest_document(file: UploadFile = File(...)):
    file_path = os.path.join(DATA_DIR, file.filename)
    with open(file_path, "wb") as buffer:
        buffer.write(await file.read())

    file_hash = hashlib.sha256(open(file_path, 'rb').read()).hexdigest()

    conn = get_db_connection()
    cursor = conn.cursor()
    
    existing_doc = cursor.execute("SELECT doc_id FROM documents WHERE hash = ?", (file_hash,)).fetchone()
    if existing_doc:
        conn.close()
        os.remove(file_path)
        return {"message": f"Document '{file.filename}' with this content has already been ingested."}

    cursor.execute("INSERT INTO documents (filename, hash) VALUES (?, ?)", (file.filename, file_hash))
    doc_id = cursor.lastrowid
    conn.commit()

    # --- CHANGE: Add logic to select parser based on file extension ---
    text_content = ""
    if file.filename.lower().endswith('.pdf'):
        print(f"Detected PDF file: {file.filename}. Using PDF parser.")
        text_content = parse_pdf(file_path)
    elif file.filename.lower().endswith(('.html', '.htm')):
        print(f"Detected HTML file: {file.filename}. Using HTML parser.")
        text_content = parse_html(file_path)
    else:
        os.remove(file_path)
        conn.close()
        raise HTTPException(status_code=400, detail="Unsupported file type. Please upload a PDF or HTML file.")
    # --- END CHANGE ---
    
    os.remove(file_path)

    chunks = chunk_text(text_content)
    
    chunk_ids = []
    for chunk in chunks:
        cursor.execute("INSERT INTO chunks (doc_id, chunk_text) VALUES (?, ?)", (doc_id, chunk))
        chunk_ids.append(f"chunk_{cursor.lastrowid}")
    conn.commit()
    conn.close()

    index_chunks(chunk_ids, chunks)

    return {"message": f"Successfully ingested and indexed '{file.filename}'. Found {len(chunks)} chunks."}


@app.post("/generate_specification", summary="Generate a spec from a requirement")
async def generate_spec_endpoint(request: Dict[str, str]):
    query = request.get("query")
    if not query:
        raise HTTPException(status_code=400, detail="Query is required.")

    context = hybrid_search(query)
    if not context:
        raise HTTPException(status_code=404, detail="Could not find relevant context. Please ingest a document first.")

    generated_spec = generate_specification(query, context)
    
    validation_result = validate_specification(generated_spec)

    return {
        "user_query": query,
        "retrieved_context_count": len(context),
        "generated_specification": generated_spec,
        "validation": validation_result
    }