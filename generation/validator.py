# generation/validator.py
import json
import ollama
from config import VALIDATOR_MODEL
from typing import Dict, Any

def validate_specification(spec_json: Dict[str, Any]) -> Dict[str, Any]:
    """
    Performs a simple validation check on the generated specification.
    
    Args:
        spec_json: The JSON output from the generator.
        
    Returns:
        A dictionary with validation status and comments.
    """
    prompt = f"""
    You are a compliance validation agent for railway specifications.
    Please review the following JSON specification.
    Check for the following:
    1.  Logical consistency: Does the content make sense?
    2.  Completeness: Does it seem to fully address a potential requirement?
    3.  Citation check: Are the `source_refs` plausible for the content?

    Return a JSON object with your findings, like: 
    {{ "is_valid": boolean, "comments": "your feedback" }}

    Specification to validate:
    {spec_json}
    """
    
    print("\n--- Sending to Validator Model ---\n")
    
    try:
        response = ollama.chat(
            model=VALIDATOR_MODEL,
            messages=[{'role': 'user', 'content': prompt}],
            format='json' # Ollama's built-in JSON mode
        )
        validation_result = response['message']['content']
        return json.loads(validation_result)
    except Exception as e:
        print(f"Error during validation: {e}")
        return {"is_valid": False, "comments": f"Validation process failed: {e}"}