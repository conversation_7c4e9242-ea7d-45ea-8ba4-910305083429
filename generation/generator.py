# generation/generator.py
import outlines.models as models
import outlines.generate as generate

import json
import yaml
from config import GENERATOR_MODEL, OLLAMA_HOST
from typing import List, Dict, Any

# Load the JSON schema and prompt template
with open('generation/schemas/spec_v1.json', 'r') as f:
    SPEC_SCHEMA = json.load(f)

with open('generation/prompts/generation_prompt.yaml', 'r') as f:
    PROMPT_TEMPLATE = yaml.safe_load(f)['system_prompt']

# Connect to the Ollama model via Outlines
model = models.openai(GENERATOR_MODEL, base_url=f"{OLLAMA_HOST}/v1", api_key="ollama")

def generate_specification(requirement: str, context: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generates a structured JSON specification using a schema-constrained model.
    
    Args:
        requirement: The user's input requirement.
        context: A list of context chunks from the hybrid retriever.
        
    Returns:
        A dictionary representing the generated JSON specification.
    """
    # Format the context for the prompt
    formatted_context = "\n\n".join([f"Chunk ID: {c['id']}\nContent: {c['text']}" for c in context])
    
    # Create the final prompt
    prompt = PROMPT_TEMPLATE.format(
        user_requirement=requirement,
        context=formatted_context
    )
    
    print("\n--- Sending to Generator Model ---\n")
    
    # Use Outlines to generate text that conforms to the JSON schema
    # This is the core of deterministic structured output
    generator = generate.json(model, SPEC_SCHEMA)
    result_str = generator(prompt)
    
    try:
        result_json = json.loads(result_str)
        return result_json
    except json.JSONDecodeError:
        print("Error: The model output was not valid JSON, despite schema constraints.")
        return {"error": "Failed to generate valid JSON.", "output": result_str}