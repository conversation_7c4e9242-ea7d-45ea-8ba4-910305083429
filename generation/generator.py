# generation/generator.py
import json
import yaml
import ollama
from config import GENERATOR_MODEL
from typing import List, Dict, Any

# Load the JSON schema and prompt template
with open('generation/schemas/spec_v1.json', 'r') as f:
    SPEC_SCHEMA = json.load(f)

with open('generation/prompts/generation_prompt.yaml', 'r') as f:
    PROMPT_TEMPLATE = yaml.safe_load(f)['system_prompt']

def generate_specification(requirement: str, context: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Generates a structured JSON specification using a schema-constrained model.
    
    Args:
        requirement: The user's input requirement.
        context: A list of context chunks from the hybrid retriever.
        
    Returns:
        A dictionary representing the generated JSON specification.
    """
    # Format the context for the prompt
    formatted_context = "\n\n".join([f"Chunk ID: {c['id']}\nContent: {c['text']}" for c in context])
    
    # Create the final prompt
    prompt = PROMPT_TEMPLATE.format(
        user_requirement=requirement,
        context=formatted_context
    )
    
    print("\n--- Sending to Generator Model ---\n")
    
    # Create the final prompt with JSON schema instructions
    schema_str = json.dumps(SPEC_SCHEMA, indent=2)
    full_prompt = f"""
{prompt}

Please respond with a valid JSON object that follows this exact schema:
{schema_str}

Make sure your response is valid JSON and follows the schema exactly.
"""

    try:
        # Use Ollama directly with JSON format
        response = ollama.chat(
            model=GENERATOR_MODEL,
            messages=[{'role': 'user', 'content': full_prompt}],
            format='json'  # This tells Ollama to output JSON
        )

        result_str = response['message']['content']
        result_json = json.loads(result_str)
        return result_json

    except json.JSONDecodeError as e:
        print(f"Error: Failed to parse generated JSON: {e}")
        return {"error": f"Failed to parse generated JSON: {e}", "raw_output": result_str if 'result_str' in locals() else "No output"}
    except Exception as e:
        print(f"Error: Generation failed: {e}")
        return {"error": f"Generation failed: {e}"}