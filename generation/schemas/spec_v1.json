{"title": "TechnicalSpecification", "type": "object", "properties": {"specification_id": {"type": "string", "description": "A unique identifier for this specification, e.g., 'spec_R-327'."}, "title": {"type": "string", "description": "A concise title for the specification."}, "sections": {"type": "array", "items": {"type": "object", "properties": {"section_id": {"type": "string", "description": "A numbered section ID, e.g., '1.4'."}, "title": {"type": "string", "description": "The title of the section."}, "content": {"type": "string", "description": "The main content of the specification section. Every assertive sentence must cite a source reference."}, "source_refs": {"type": "array", "items": {"type": "string"}, "description": "An array of chunk IDs from the context that support the content."}}, "required": ["section_id", "title", "content", "source_refs"]}}}, "required": ["specification_id", "title", "sections"]}