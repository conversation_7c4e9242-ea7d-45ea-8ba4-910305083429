{"test": 0, "railway": 1, "requirements": 2, "safety": 3, "system": 4, "shall": 5, "meet": 6, "sil": 7, "integrity": 8, "level": 9, "braking": 10, "must": 11, "fail": 12, "safe": 13, "respond": 14, "within": 15, "seconds": 16, "all": 17, "critical": 18, "components": 19, "have": 20, "redundancy": 21, "operate": 22, "temperatures": 23, "from": 24, "40": 25, "70": 26, "additional": 27, "operational": 28, "train": 29, "control": 30, "maintain": 31, "communication": 32, "central": 33, "dispatch": 34, "emergency": 35, "activated": 36, "automatically": 37, "case": 38, "signal": 39, "failure": 40, "log": 41, "events": 42, "audit": 43, "purposes": 44, "power": 45, "systems": 46, "backup": 47, "generators": 48, "operations": 49, "sensors": 50, "tested": 51, "monthly": 52, "proper": 53, "calibration": 54, "signaling": 55, "comply": 56, "etcs": 57, "standards": 58, "track": 59, "monitoring": 60, "detect": 61, "obstacles": 62, "500": 63, "meters": 64, "weather": 65, "provide": 66, "real": 67, "time": 68, "data": 69, "operators": 70, "maintenance": 71, "schedules": 72, "generated": 73, "based": 74, "usage": 75, "driver": 76, "alertness": 77, "active": 78, "during": 79, "performance": 80, "efficiency": 81, "maximum": 82, "speed": 83, "exceed": 84, "300": 85, "km": 86, "high": 87, "lines": 88, "energy": 89, "targets": 90, "achieve": 91, "95": 92, "regenerative": 93, "recovery": 94, "passenger": 95, "capacity": 96, "optimization": 97, "comfort": 98, "station": 99, "dwell": 100, "times": 101, "90": 102, "express": 103, "services": 104, "network": 105, "availability": 106, "maintained": 107, "99": 108, "uptime": 109, "annually": 110, "noise": 111, "levels": 112, "environmental": 113, "regulations": 114, "urban": 115, "areas": 116, "vibration": 117, "ensure": 118, "climate": 119, "temperature": 120, "variance": 121, "door": 122, "operation": 123, "complete": 124, "cycles": 125, "platform": 126, "gap": 127, "boarding": 128, "information": 129, "update": 130, "every": 131, "30": 132, "accessibility": 133, "features": 134, "disability": 135, "access": 136, "comprehensive": 137, "defined": 138, "en": 139, "50128": 140, "50129": 141, "includes": 142, "hazard": 143, "analysis": 144, "risk": 145, "assessment": 146, "documentation": 147, "functions": 148, "implemented": 149, "appropriate": 150, "mechanisms": 151, "undergo": 152, "independent": 153, "certified": 154, "assessor": 155, "before": 156, "commissioning": 157, "activation": 158, "command": 159, "triggered": 160, "violations": 161, "limit": 162, "exceedances": 163, "obstacle": 164, "detection": 165, "distance": 166, "calculation": 167, "account": 168, "conditions": 169, "load": 170, "brake": 171, "continuously": 172, "verify": 173, "effectiveness": 174, "alert": 175, "any": 176, "degradation": 177, "automatic": 178, "switchover": 179, "capabilities": 180, "equipment": 181, "networks": 182, "supplies": 183, "redundant": 184, "independently": 185, "monitored": 186, "regularly": 187, "single": 188, "component": 189, "compromise": 190, "overall": 191, "reliably": 192, "ranging": 193, "humidity": 194, "up": 195, "non": 196, "condensing": 197, "protected": 198, "against": 199, "electromagnetic": 200, "interference": 201, "shock": 202, "specified": 203, "relevant": 204, "weatherproofing": 205, "continued": 206, "rain": 207, "snow": 208, "extreme": 209, "continuous": 210, "using": 211, "multiple": 212, "channels": 213, "including": 214, "radio": 215, "gsm": 216, "fiber": 217, "optic": 218, "ensures": 219, "continuity": 220, "even": 221, "failures": 222, "outages": 223, "communications": 224, "encrypted": 225, "authenticated": 226, "prevent": 227, "unauthorized": 228, "procedures": 229, "clearly": 230, "practiced": 231, "staff": 232, "evacuation": 233, "fire": 234, "suppression": 235, "coordination": 236, "direct": 237, "contact": 238, "between": 239, "trains": 240, "centers": 241, "responders": 242, "determined": 243, "classification": 244, "rolling": 245, "stock": 246, "specifications": 247, "may": 248, "advanced": 249, "protection": 250, "restrictions": 251, "enforced": 252, "include": 253, "achieving": 254, "optimizing": 255, "traction": 256, "consumption": 257, "smart": 258, "grid": 259, "integration": 260, "enable": 261, "dynamic": 262, "balancing": 263, "renewable": 264, "utilization": 265, "patterns": 266, "identify": 267, "opportunities": 268, "planned": 269, "windows": 270, "scheduled": 271, "low": 272, "traffic": 273, "periods": 274, "predictive": 275, "monitor": 276, "condition": 277, "schedule": 278, "interventions": 279, "occur": 280, "spare": 281, "parts": 282, "inventory": 283, "optimized": 284, "minimize": 285, "downtime": 286, "while": 287, "controlling": 288, "costs": 289, "service": 290, "updates": 291, "delays": 292, "disruptions": 293, "multi": 294, "language": 295, "support": 296, "accommodate": 297, "diverse": 298, "needs": 299, "visual": 300, "audio": 301, "announcements": 302, "synchronized": 303, "audible": 304, "throughout": 305, "comfortable": 306, "air": 307, "quality": 308, "compartments": 309, "set": 310, "points": 311, "circulation": 312, "health": 313, "ventilation": 314, "activate": 315, "smoke": 316, "compliance": 317, "equal": 318, "passengers": 319, "disabilities": 320, "wheelchair": 321, "tactile": 322, "guidance": 323, "aids": 324, "automated": 325, "assist": 326, "alighting": 327, "safely": 328, "detailed": 329, "specification": 330, "section": 331, "overview": 332, "represents": 333, "complex": 334, "subsystems": 335, "working": 336, "together": 337, "efficient": 338, "reliable": 339, "freight": 340, "transportation": 341, "encompasses": 342, "infrastructure": 343, "supply": 344, "each": 345, "stringent": 346, "maintaining": 347, "interoperability": 348, "existing": 349, "architecture": 350, "follows": 351, "hierarchical": 352, "approach": 353, "centralized": 354, "distributed": 355, "execution": 356, "manages": 357, "local": 358, "handle": 359, "specific": 360, "management": 361, "distribution": 362, "both": 363, "fault": 364, "tolerance": 365, "through": 366, "graceful": 367, "requires": 368, "careful": 369, "consideration": 370, "legacy": 371, "upgrade": 372, "paths": 373, "migration": 374, "strategies": 375, "backward": 376, "compatibility": 377, "transition": 378, "new": 379, "gradually": 380, "introduced": 381, "design": 382, "future": 383, "expansion": 384, "technology": 385, "evolution": 386, "without": 387, "requiring": 388, "replacement": 389, "assets": 390, "reliability": 391, "assigned": 392, "interlocking": 393, "encompass": 394, "supervision": 395, "route": 396, "setting": 397, "crossing": 398, "lower": 399, "apply": 400, "achieved": 401, "layers": 402, "hardware": 403, "software": 404, "diversity": 405, "procedural": 406, "safeguards": 407, "employ": 408, "out": 409, "voting": 410, "architectures": 411, "self": 412, "principles": 413, "results": 414, "state": 415, "typically": 416, "involving": 417, "restrictive": 418, "signals": 419, "established": 420, "subsystem": 421, "considerations": 422, "mean": 423, "mtbf": 424, "drive": 425, "selection": 426, "unplanned": 427, "remains": 428, "acceptable": 429, "limits": 430, "punctuality": 431, "metrics": 432, "line": 433, "speeds": 434, "geometry": 435, "characteristics": 436, "calculations": 437, "consider": 438, "headway": 439, "conflicts": 440, "require": 441, "planning": 442, "motors": 443, "enables": 444, "scheduling": 445, "supports": 446, "sustainability": 447, "goals": 448, "cost": 449, "addresses": 450, "emissions": 451, "impact": 452, "barriers": 453, "damping": 454, "community": 455, "isolation": 456, "protects": 457, "sensitive": 458, "reduces": 459, "structural": 460, "fatigue": 461, "shielding": 462, "prevents": 463, "adjacent": 464, "backbone": 465, "response": 466, "technologies": 467, "employed": 468, "cables": 469, "wireless": 470, "satellite": 471, "links": 472, "implement": 473, "atp": 474, "ato": 475, "ats": 476, "unsafe": 477, "movements": 478, "position": 479, "oversight": 480, "coordinates": 481, "optimizes": 482, "ctc": 483, "manage": 484, "conflict": 485, "resolution": 486, "adherence": 487, "across": 488, "entire": 489, "trackside": 490, "stations": 491, "inform": 492, "decisions": 493, "algorithms": 494, "anticipate": 495, "potential": 496, "recommend": 497, "optimal": 498, "routing": 499, "adjustments": 500, "geometric": 501, "tolerances": 502, "rail": 503, "profiles": 504, "fastening": 505, "ballast": 506, "selected": 507, "loads": 508, "welded": 509, "improves": 510, "ride": 511, "structures": 512, "bridges": 513, "tunnels": 514, "embankments": 515, "meeting": 516, "seismic": 517, "life": 518, "100": 519, "years": 520, "material": 521, "corrosion": 522, "regular": 523, "inspection": 524, "programs": 525, "electrification": 526, "delivery": 527, "minimizing": 528, "overhead": 529, "catenary": 530, "third": 531, "configurations": 532, "space": 533, "constraints": 534, "stable": 535, "voltage": 536, "frequency": 537, "align": 538, "axle": 539, "vehicle": 540, "dimensions": 541, "compatible": 542, "standardized": 543, "interfaces": 544, "different": 545, "types": 546, "manufacturers": 547, "adequate": 548, "operating": 549, "adverse": 550, "steep": 551, "grades": 552, "situations": 553, "recover": 554, "deceleration": 555, "precise": 556, "wheel": 557, "interface": 558, "wear": 559, "amenities": 560, "lighting": 561, "seating": 562, "regulatory": 563, "customer": 564, "expectations": 565, "connection": 566, "instructions": 567, "onboard": 568, "entertainment": 569, "connectivity": 570, "enhance": 571, "experience": 572, "database": 573, "mandatory": 574, "trigger": 575, "immediate": 576, "stops": 577, "circuits": 578, "presence": 579, "conflicting": 580, "routes": 581, "crossings": 582, "required": 583, "essential": 584, "need": 585, "hot": 586, "standby": 587, "diagnostics": 588, "run": 589, "constantly": 590, "alerts": 591, "tracked": 592, "testing": 593, "strictly": 594, "daily": 595, "checks": 596, "weekly": 597, "tests": 598, "functionality": 599, "assessments": 600, "check": 601, "annual": 602, "audits": 603, "training": 604, "records": 605, "certification": 606, "competency": 607, "briefings": 608, "conducted": 609, "acceleration": 610, "rates": 611, "distances": 612, "calculated": 613, "delay": 614, "priority": 615, "rules": 616, "recovers": 617, "regulation": 618, "stability": 619, "ensured": 620, "harmonic": 621, "distortion": 622, "minimized": 623, "factor": 624, "controlled": 625, "prevented": 626, "reduced": 627, "water": 628, "runoff": 629, "managed": 630, "waste": 631, "disposal": 632, "regulated": 633, "recycling": 634, "pursued": 635, "measured": 636, "repair": 637, "trending": 638, "analyzed": 639, "identified": 640, "prioritized": 641, "provided": 642, "clear": 643, "posted": 644, "security": 645, "measures": 646, "surveillance": 647, "intrusion": 648, "threat": 649, "coordinated": 650, "incident": 651, "reporting": 652, "technical": 653, "protocols": 654, "formats": 655, "documented": 656, "version": 657, "change": 658, "formal": 659, "configuration": 660, "strict": 661, "validation": 662, "methods": 663, "verification": 664, "processes": 665, "ratings": 666, "mechanical": 667, "electrical": 668, "parameters": 669, "thermal": 670, "designed": 671, "resistance": 672, "verified": 673, "applied": 674, "guaranteed": 675, "memory": 676, "processing": 677, "error": 678, "handling": 679, "robust": 680, "logging": 681, "extensive": 682, "debugging": 683, "tools": 684, "secure": 685, "preventive": 686, "corrective": 687, "tool": 688, "personnel": 689, "qualifications": 690, "intervals": 691, "frequencies": 692, "criteria": 693, "modification": 694, "controls": 695, "approval": 696, "evaluations": 697, "analyses": 698, "updated": 699, "work": 700, "order": 701, "activities": 702, "collected": 703, "trend": 704, "identifies": 705, "issues": 706, "tracking": 707, "resource": 708, "coordinate": 709, "progress": 710, "completion": 711, "assurance": 712, "verifies": 713, "satisfaction": 714, "era": 715, "unisig": 716, "eeig": 717, "ertms": 718, "users": 719, "group": 720, "subset": 721, "130": 722, "ob": 723, "fffis": 724, "application": 725, "layer": 726, "page": 727, "25": 728, "ref": 729, "issue": 730, "date": 731, "05": 732, "07": 733, "23": 734, "history": 735, "number": 736, "description": 737, "author": 738, "12": 739, "11": 740, "2014": 741, "document": 742, "wp": 743, "14": 744, "content": 745, "has": 746, "been": 747, "changed": 748, "according": 749, "comments": 750, "referring": 751, "last": 752, "20": 753, "02": 754, "2015": 755, "eug": 756, "09": 757, "2016": 758, "structure": 759, "10": 760, "review": 761, "19": 762, "22": 763, "2017": 764, "sg": 765, "draft": 766, "18": 767, "01": 768, "2018": 769, "15": 770, "03": 771, "aligned": 772, "125": 773, "0018": 774, "packets": 775, "drafta": 776, "16": 777, "04": 778, "0019": 779, "0011": 780, "010": 781, "remaining": 782, "2020": 783, "changes": 784, "eect": 785, "57": 786, "68": 787, "21": 788, "06": 789, "2021": 790, "consolidation": 791, "phase": 792, "add": 793, "timeout": 794, "values": 795, "list": 796, "m_mode": 797, "remove": 798, "q_admode": 799, "column": 800, "cr": 801, "s125": 802, "192": 803, "139": 804, "id": 805, "variable": 806, "shifted": 807, "q_dircontroller": 808, "ss": 809, "properly": 810, "removed": 811, "d_antenna": 812, "due": 813, "066": 814, "restore": 815, "27": 816, "08": 817, "nid_active_antenna_lrbg": 818, "moved": 819, "one": 820, "item": 821, "offset": 822, "clean": 823, "uinisg": 824, "remarks": 825, "78": 826, "t_dwelltime": 827, "antenna": 828, "topic": 829, "2022": 830, "81": 831, "type": 832, "over": 833, "none": 834, "2023": 835, "91": 836, "cr1344": 837, "cr1370": 838, "removal": 839, "watermark": 840, "24": 841, "94": 842, "dates": 843, "move": 844, "back": 845, "mistake": 846, "when": 847, "creating": 848, "v0": 849, "baseline": 850, "1st": 851, "release": 852, "unsig": 853, "table": 854, "contents": 855, "tables": 856, "introduction": 857, "scope": 858, "purpose": 859, "reference": 860, "documents": 861, "abbreviations": 862, "definitions": 863, "definition": 864, "variables": 865, "packet": 866, "user": 867, "summary": 868, "ato_etcs_status": 869, "ato_etcs_dmi": 870, "ato_etcs_data_entry_need": 871, "13": 872, "ato_etcs_data_entry_request": 873, "ato_etcs_data_view_values": 874, "etcs_ato_static": 875, "etcs_ato_dynamic": 876, "etcs_ato_driver_inputs": 877, "etcs_ato_data_entry_values": 878, "etcs_ato_data_entry_flag": 879, "etcs_ato_brake_decelerations": 880, "define": 881, "details": 882, "standardised": 883, "transmit": 884, "associated": 885, "included": 886, "requirement": 887, "026": 888, "title": 889, "ts": 890, "126": 891, "glossary": 892, "13e154": 893, "fis": 894, "juridical": 895, "recording": 896, "027": 897, "dimensioning": 898, "engineering": 899, "040": 900, "ieee": 901, "802": 902, "ethernet": 903, "standard": 904, "na": 905, "stm": 906, "058": 907, "terms": 908, "023": 909, "board": 910, "143": 911, "related": 912, "see": 913, "already": 914, "used": 915, "same": 916, "name": 917, "giving": 918, "corresponding": 919, "its": 920, "formula": 921, "length": 922, "part": 923, "bitset": 924, "needed": 925, "amount": 926, "added": 927, "most": 928, "significant": 929, "bits": 930, "following": 931, "chapters": 932, "describe": 933, "only": 934, "way": 935, "how": 936, "transformed": 937, "can": 938, "found": 939, "grouped": 940, "unit": 941, "internal": 942, "source": 943, "sink": 944, "transmitting": 945, "cycle": 946, "ms": 947, "class": 948, "process": 949, "1000": 950, "message": 951, "3000": 952, "200": 953, "etcs_ato_data_view_values_": 954, "request": 955, "etcs_ato_brake_decelera": 956, "tions": 957, "which": 958, "sent": 959, "event": 960, "numbers": 961, "correspond": 962, "nid_packet": 963, "given": 964, "uses": 965, "slot": 966, "bit": 967, "ato_info_set": 968, "bitset8": 969, "q_ad_mode_request": 970, "qualifier": 971, "ad": 972, "mode": 973, "requested": 974, "ato_dmi_info": 975, "dmi": 976, "indicators": 977, "bitset16": 978, "m_atostatus": 979, "status": 980, "displayed": 981, "ready": 982, "engagement": 983, "engaged": 984, "disengaging": 985, "q_stopaccuracy": 986, "stopping": 987, "accuracy": 988, "indication": 989, "accurate": 990, "stop": 991, "undershoot": 992, "overshoot": 993, "q_dwelltime_info": 994, "hold": 995, "q_doorinfo": 996, "close": 997, "doors": 998, "open": 999, "sides": 1000, "right": 1001, "left": 1002, "closed": 1003, "being": 1004, "q_skipstp": 1005, "skip": 1006, "point": 1007, "inactive": 1008, "q_coasting": 1009, "coasting": 1010, "advice": 1011, "q_warningsound": 1012, "indicating": 1013, "requesting": 1014, "produce": 1015, "warning": 1016, "sound": 1017, "uint16": 1018, "special": 1019, "value": 1020, "65535": 1021, "v_tas": 1022, "target": 1023, "cm": 1024, "16668": 1025, "65534": 1026, "d_nextadvice": 1027, "next": 1028, "uint32": 1029, "232": 1030, "t_next_stp_arrival_time": 1031, "arrival": 1032, "skipped": 1033, "00": 1034, "86400": 1035, "interpreted": 1036, "ects": 1037, "86401": 1038, "l_text_stp": 1039, "text": 1040, "string": 1041, "bytes": 1042, "uint8": 1043, "display": 1044, "x_text_stp": 1045, "120": 1046, "n_stpdistance_iter": 1047, "elements": 1048, "d_stpdistance": 1049, "ato_data_entry_need": 1050, "q_ato_dataentry": 1051, "n_der_iter": 1052, "end": 1053, "entry": 1054, "255": 1055, "nid_data_ato": 1056, "identifier": 1057, "l_caption": 1058, "x_caption": 1059, "119": 1060, "l_value": 1061, "x_value": 1062, "121": 1063, "n_dkv_iter": 1064, "dedicated": 1065, "keyboard": 1066, "n_dvv_iter": 1067, "view": 1068, "etcs_data": 1069, "valid": 1070, "qualifiers": 1071, "q_train_data_": 1072, "q_operationa": 1073, "l_data_valid": 1074, "nid_operational": 1075, "driver_id": 1076, "nid_engine": 1077, "88": 1078, "nid_antenna": 1079, "identification": 1080, "front": 1081, "n_antenna_iter": 1082, "antennas": 1083, "installed": 1084, "l_train": 1085, "q_train_data_valid": 1086, "56": 1087, "v_maxtrain": 1088, "160": 1089, "nc_cdtrain": 1090, "82": 1091, "nc_train": 1092, "84": 1093, "m_axleloadcat": 1094, "62": 1095, "m_nom_rot_mass": 1096, "m_brake_percentage_": 1097, "percentage": 1098, "251": 1099, "254": 1100, "models": 1101, "captured": 1102, "17": 1103, "m_brake_position_ato": 1104, "q_index_gamma_conf": 1105, "full": 1106, "preconfigured": 1107, "currently": 1108, "applicable": 1109, "capture": 1110, "acquired": 1111, "conversion": 1112, "model": 1113, "q_operational_data_valid": 1114, "92": 1115, "bcd32": 1116, "string16": 1117, "etcs_ato_info_set": 1118, "m_adhesion_driver": 1119, "adhesion": 1120, "slippery": 1121, "q_appcond": 1122, "fulfilled": 1123, "q_rc": 1124, "indicate": 1125, "whether": 1126, "movement": 1127, "direction": 1128, "orientation": 1129, "corresponds": 1130, "increase": 1131, "decrease": 1132, "counter": 1133, "unknown": 1134, "positioning": 1135, "position_report_set": 1136, "report": 1137, "q_dirsolr": 1138, "q_dsolr": 1139, "n_loc_ref": 1140, "moment": 1141, "int32": 1142, "231": 1143, "note": 1144, "other": 1145, "depend": 1146, "n_loc_refbalise": 1147, "t_loc_ref": 1148, "center": 1149, "balise": 1150, "location": 1151, "solr": 1152, "duplicating": 1153, "nid_active_antenna_solr": 1154, "passed": 1155, "nid_refbalise": 1156, "bitset32": 1157, "bit00": 1158, "bit02": 1159, "n_pig": 1160, "bit03": 1161, "bit16": 1162, "nid_bg": 1163, "85": 1164, "bit17": 1165, "bit26": 1166, "nid_c": 1167, "86": 1168, "bit27": 1169, "bit31": 1170, "t_loc_refbalise": 1171, "n_loc_baliserunover1": 1172, "nid_active_antenna_bro1": 1173, "nid_baliserunover1": 1174, "t_loc_baliserunover1": 1175, "n_loc_baliserunover2": 1176, "nid_active_antenna_bro2": 1177, "nid_baliserunover2": 1178, "t_loc_baliserunover2": 1179, "l_uncertainty_overreading": 1180, "reading": 1181, "confidence": 1182, "interval": 1183, "referred": 1184, "l_uncertainty_underreadin": 1185, "under": 1186, "72": 1187, "n_loc_ebi": 1188, "fs": 1189, "os": 1190, "estimated": 1191, "closest": 1192, "current": 1193, "ebi": 1194, "supervised": 1195, "26": 1196, "a_gradient": 1197, "gradient": 1198, "int16": 1199, "mm": 1200, "s2": 1201, "2500": 1202, "negative": 1203, "declining": 1204, "positive": 1205, "inclining": 1206, "32768": 1207, "2501": 1208, "32766": 1209, "32767": 1210, "n_grad_iter": 1211, "available": 1212, "52": 1213, "28": 1214, "n_loc_gradchange": 1215, "allowed": 1216, "29": 1217, "profile": 1218, "a_maxredadh": 1219, "31": 1220, "n_adhe_iter": 1221, "announced": 1222, "32": 1223, "n_loc_adhchange": 1224, "33": 1225, "34": 1226, "v_mrsp": 1227, "minimum": 1228, "35": 1229, "n_mrsp_iter": 1230, "mrsp": 1231, "iterations": 1232, "51": 1233, "36": 1234, "n_loc_mrsp": 1235, "compensation": 1236, "37": 1237, "numerical": 1238, "telling": 1239, "ends": 1240, "38": 1241, "n_loc_eoaloa": 1242, "eoa": 1243, "loa": 1244, "temporary": 1245, "eoas": 1246, "nor": 1247, "39": 1248, "v_eoaloa": 1249, "permitted": 1250, "n_loc_svl": 1251, "svl": 1252, "41": 1253, "t_traction": 1254, "effort": 1255, "still": 1256, "present": 1257, "after": 1258, "intervention": 1259, "standstill": 1260, "expected": 1261, "build": 1262, "reduction": 1263, "per": 1264, "42": 1265, "t_traction_speed": 1266, "43": 1267, "t_be_react": 1268, "reaction": 1269, "yet": 1270, "force": 1271, "starts": 1272, "44": 1273, "t_berem": 1274, "until": 1275, "reached": 1276, "45": 1277, "t_berem_speed": 1278, "46": 1279, "v_permitted": 1280, "47": 1281, "v_release_ato": 1282, "48": 1283, "n_loc_rsm": 1284, "rsm": 1285, "start": 1286, "49": 1287, "v_est": 1288, "odometry": 1289, "50": 1290, "v_delta0": 1291, "inaccuracy": 1292, "measurement": 1293, "a_est": 1294, "5000": 1295, "5001": 1296, "linking": 1297, "n_link_iter": 1298, "linked": 1299, "groups": 1300, "53": 1301, "n_loc_linknbg": 1302, "referenced": 1303, "54": 1304, "nid_linknbg": 1305, "always": 1306, "n_atoengage_selection": 1307, "engage": 1308, "n_skipstpreq_selection": 1309, "n_skipstprev_selection": 1310, "revocation": 1311, "n_dev_iter": 1312, "ato_data_entry_flag": 1313, "m_ato_dataentryflag": 1314, "beginning": 1315, "procedure": 1316, "etcs_ato_data_view_values_request": 1317, "does": 1318, "contain": 1319, "a_brake_safe": 1320, "zero": 1321, "n_brake_safe_iter": 1322, "v_change_brake": 1323, "excluding": 1324, "n_sebdm_iter": 1325, "inhibition": 1326, "brakes": 1327, "n_loc_sebdm_change": 1328, "": 1329}