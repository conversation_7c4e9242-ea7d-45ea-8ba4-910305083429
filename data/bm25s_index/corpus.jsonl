{"id":0,"text":"Test Railway Requirements\nRailway Safety Requirements\nThe railway system shall meet SIL 2 safety integrity level requirements. The braking system must be fail-safe and respond within 2 seconds. All safety-critical components shall have redundancy. The system shall operate in temperatures from -40°C to +70°C."}
{"id":1,"text":"Additional Railway Requirements\nRailway Operational Requirements\nThe train control system shall maintain communication with the central dispatch. Emergency braking shall be activated automatically in case of signal failure. The system shall log all safety events for audit purposes. Power systems must have backup generators for critical operations. All sensors must be tested monthly for proper calibration. The signaling system shall comply with ETCS Level 2 standards. Track monitoring systems shall detect obstacles within 500 meters. Weather monitoring shall provide real-time data to operators. Maintenance schedules shall be automatically generated based on usage. Driver alertness monitoring shall be active during all operations."}
{"id":2,"text":"Railway Performance Requirements\nPerformance and Efficiency Standards\nMaximum train speed shall not exceed 300 km/h on high-speed lines. Energy efficiency targets must achieve 95% regenerative braking recovery. Passenger capacity optimization shall maintain comfort standards. Station dwell times shall not exceed 90 seconds for express services. Network availability must be maintained at 99.5% uptime annually. Noise levels shall comply with environmental regulations in urban areas. Vibration monitoring shall ensure passenger comfort and track integrity. Climate control systems shall maintain temperature within 2°C variance. Door operation systems shall complete cycles within 8 seconds. Platform gap monitoring shall ensure safe passenger boarding. Real-time passenger information systems shall update every 30 seconds. Accessibility features shall comply with disability access standards."}
{"id":3,"text":"Comprehensive Railway System Requirements\nRailway System Safety Requirements\nThe railway system shall meet SIL 2 safety integrity level requirements as defined in EN 50128 and EN 50129 standards. This includes comprehensive hazard analysis, risk assessment, and safety case documentation. All safety-critical functions must be implemented with appropriate redundancy and fail-safe mechanisms. The system shall undergo independent safety assessment by a certified assessor before commissioning. The braking system must be fail-safe and respond within 2 seconds of activation command. Emergency braking shall be automatically triggered in case of signal violations, speed limit exceedances, or obstacle detection. The braking distance calculation must account for track conditions, weather, and train load. Brake performance monitoring shall continuously verify system effectiveness and alert operators to any degradation. All safety-critical components shall have redundancy with automatic switchover capabilities. This includes signaling equipment, train control systems, communication networks, and power supplies. The redundant systems shall be independently monitored and tested regularly. Failure of any single component shall not compromise overall system safety or availability. Environmental and Operational Requirements\nThe system shall operate reliably in temperatures ranging from -40°C to +70°C with humidity levels up to 95% non-condensing. Equipment shall be protected against electromagnetic interference, vibration, and shock as specified in relevant railway standards. Weatherproofing shall ensure continued operation during rain, snow, and extreme weather conditions. Train control systems shall maintain continuous communication with central dispatch using multiple communication channels including radio, GSM-R, and fiber optic networks. Communication redundancy ensures operational continuity even during equipment failures or network outages. All communications shall be encrypted and authenticated to prevent unauthorized access. Emergency procedures shall be clearly defined and regularly practiced by all operational staff. This includes evacuation procedures, fire suppression systems, and coordination with emergency services. Emergency communication systems shall provide direct contact between trains, control centers, and emergency responders. Backup power systems shall maintain critical operations during power outages. Performance and Efficiency Standards\nMaximum operational speed shall be determined by track classification, signaling system capabilities, and rolling stock specifications. High-speed lines may operate up to 300 km/h with appropriate safety systems including continuous track monitoring, advanced signaling, and automatic train protection. Speed restrictions shall be automatically enforced by the train control system. Energy efficiency targets include achieving 95% regenerative braking energy recovery and optimizing traction power consumption. Smart grid integration shall enable dynamic load balancing and renewable energy utilization. Energy monitoring systems shall track consumption patterns and identify optimization opportunities. Network availability must maintain 99.5% uptime annually with planned maintenance windows scheduled during low-traffic periods. Predictive maintenance systems shall monitor equipment condition and schedule interventions before failures occur. Spare parts inventory shall be optimized to minimize downtime while controlling costs. Passenger Service Requirements\nPassenger information systems shall provide real-time updates on train schedules, delays, and service disruptions. Multi-language support and accessibility features shall accommodate diverse passenger needs. Visual and audio announcements shall be synchronized and clearly audible throughout all passenger areas. Climate control systems shall maintain comfortable temperature and air quality in all passenger compartments."}
{"id":4,"text":"Visual and audio announcements shall be synchronized and clearly audible throughout all passenger areas. Climate control systems shall maintain comfortable temperature and air quality in all passenger compartments. Temperature variance shall not exceed 2°C from set points, and air circulation shall meet health and safety standards. Emergency ventilation systems shall activate automatically in case of fire or smoke detection. Accessibility compliance shall ensure equal access for passengers with disabilities including wheelchair accessibility, tactile guidance systems, and audio-visual aids. Platform gap monitoring and automated announcements shall assist passengers with boarding and alighting safely."}
{"id":5,"text":"Detailed Railway System Specification\nSection 1: System Overview\nThe railway system represents a complex integration of multiple subsystems working together to provide safe, efficient, and reliable passenger and freight transportation. The system encompasses track infrastructure, rolling stock, signaling and control systems, power supply networks, communication systems, and operational procedures. Each component must meet stringent safety and performance requirements while maintaining interoperability with existing railway networks. System architecture follows a hierarchical approach with centralized control and distributed execution. The central control system manages overall network operations, while local subsystems handle specific functions such as train control, signal management, and power distribution. This architecture ensures both operational efficiency and fault tolerance through redundancy and graceful degradation capabilities. Integration with existing infrastructure requires careful consideration of legacy systems, upgrade paths, and migration strategies. Backward compatibility must be maintained during transition periods, while new capabilities are gradually introduced. The system design must accommodate future expansion and technology evolution without requiring complete replacement of existing assets. Section 2: Safety and Reliability\nSafety integrity levels (SIL) are assigned to all safety-critical functions based on hazard analysis and risk assessment. SIL 4 functions include automatic train protection, emergency braking, and signal interlocking. SIL 3 functions encompass speed supervision, route setting, and level crossing protection. Lower SIL levels apply to non-critical functions such as passenger information and comfort systems. Fault tolerance is achieved through multiple layers of protection including hardware redundancy, software diversity, and procedural safeguards. Critical systems employ 2-out-of-3 voting architectures with continuous self-monitoring and automatic fault detection. Fail-safe principles ensure that any single failure results in a safe state, typically involving restrictive signals or emergency braking. Reliability targets are established for each subsystem based on operational requirements and safety considerations. Mean time between failures (MTBF) specifications drive component selection and maintenance strategies. Availability requirements ensure that planned and unplanned downtime remains within acceptable limits for passenger service and freight operations. Section 3: Performance Specifications\nOperational performance encompasses speed, capacity, punctuality, and energy efficiency metrics. Maximum line speeds are determined by track geometry, signaling system capabilities, and rolling stock characteristics. Capacity calculations consider headway times, station dwell periods, and route conflicts. Punctuality targets require coordination between infrastructure capacity and operational planning. Energy efficiency optimization includes regenerative braking systems, efficient traction motors, and smart grid integration. Power consumption monitoring enables real-time optimization and predictive maintenance scheduling. Renewable energy integration supports sustainability goals while maintaining operational reliability and cost effectiveness. Environmental performance addresses noise emissions, vibration levels, electromagnetic compatibility, and visual impact. Noise barriers and track damping systems minimize community impact. Vibration isolation protects sensitive equipment and reduces structural fatigue. Electromagnetic shielding prevents interference with adjacent systems and communication networks. Section 4: Communication and Control\nCommunication networks provide the backbone for all system operations including train control, passenger information, maintenance coordination, and emergency response. Multiple communication technologies are employed including fiber optic cables, wireless networks, and satellite links. Redundant communication paths ensure continued operation during equipment failures or network disruptions."}
{"id":6,"text":"Multiple communication technologies are employed including fiber optic cables, wireless networks, and satellite links. Redundant communication paths ensure continued operation during equipment failures or network disruptions. Train control systems implement automatic train protection (ATP), automatic train operation (ATO), and automatic train supervision (ATS) functions. ATP prevents unsafe train movements through continuous speed and position monitoring. ATO enables automated train operation while maintaining safety oversight. ATS coordinates multiple trains and optimizes network capacity utilization. Centralized traffic control (CTC) systems manage route setting, conflict resolution, and schedule adherence across the entire network. Real-time data from trackside sensors, train systems, and weather stations inform operational decisions. Predictive algorithms anticipate potential conflicts and recommend optimal routing and scheduling adjustments. Section 5: Infrastructure Requirements\nTrack infrastructure must support design speeds while maintaining geometric tolerances for passenger comfort and safety. Rail profiles, fastening systems, and ballast specifications are selected based on traffic loads, environmental conditions, and maintenance requirements. Continuous welded rail reduces maintenance needs and improves ride quality. Structures including bridges, tunnels, and embankments must accommodate dynamic loads from high-speed trains while meeting seismic and environmental requirements. Design life targets of 100+ years require careful material selection and corrosion protection. Regular inspection and monitoring programs ensure structural integrity throughout the service life. Electrification systems provide reliable power delivery to trains while minimizing electromagnetic interference and environmental impact. Overhead catenary systems or third rail configurations are selected based on operational requirements and space constraints. Power quality monitoring ensures stable voltage and frequency for optimal train performance. Section 6: Rolling Stock Integration\nRolling stock specifications must align with infrastructure capabilities and operational requirements. Axle loads, vehicle dimensions, and braking performance must be compatible with track and signaling systems. Standardized interfaces enable interoperability between different vehicle types and manufacturers. Traction and braking systems must provide adequate performance across all operating conditions including adverse weather, steep grades, and emergency situations. Regenerative braking systems recover energy during deceleration while maintaining precise speed control. Wheel-rail interface optimization reduces wear and maintenance requirements. Passenger amenities include climate control, lighting, seating, and accessibility features that meet regulatory requirements and customer expectations. Real-time passenger information systems provide schedule updates, connection information, and emergency instructions. Onboard entertainment and connectivity services enhance the passenger experience."}
{"id":7,"text":"Railway Requirements Database\nSafety Requirements\nThe system shall meet SIL 2 requirements. All components must be fail-safe. Emergency braking is mandatory. Speed limits must be enforced automatically. Signal violations trigger immediate stops. Track circuits detect train presence. Interlocking prevents conflicting routes. Level crossings have automatic protection. Fire detection systems are required. Emergency communication is essential. Redundancy is required for critical systems. Backup power supplies are mandatory. Communication systems need multiple paths. Control systems require hot standby. Monitoring systems must be continuous. Fault detection is automatic. Self-diagnostics run constantly. Maintenance alerts are generated automatically. System health is monitored continuously. Performance metrics are tracked. Testing procedures are strictly defined. Daily checks are mandatory. Weekly tests verify functionality. Monthly assessments check safety. Annual audits ensure compliance. Documentation must be complete. Training records are maintained. Certification is required for operators. Competency testing is regular. Safety briefings are conducted. Operational Requirements\nMaximum speed is 300 km/h. Acceleration rates are specified. Braking distances are calculated. Station dwell times are optimized. Headway times ensure capacity. Schedule adherence is monitored. Delay recovery is planned. Route optimization is automatic. Conflict resolution is immediate. Priority rules are enforced. Energy efficiency targets are set. Regenerative braking recovers power. Traction optimization reduces consumption. Smart grid integration is implemented. Load balancing is automatic. Power quality is monitored. Voltage regulation is maintained. Frequency stability is ensured. Harmonic distortion is minimized. Power factor is optimized. Environmental compliance is mandatory. Noise levels are controlled. Vibration is minimized. Electromagnetic interference is prevented. Visual impact is reduced. Air quality is maintained. Water runoff is managed. Waste disposal is regulated. Recycling programs are implemented. Sustainability goals are pursued. Performance Standards\nAvailability targets are 99.5%. Reliability metrics are tracked. Mean time between failures is measured. Mean time to repair is minimized. Spare parts availability is ensured. Maintenance windows are scheduled. Predictive maintenance is implemented. Condition monitoring is continuous. Performance trending is analyzed. Optimization opportunities are identified. Passenger comfort is prioritized. Temperature control is automatic. Air quality is monitored. Lighting levels are optimized. Noise levels are controlled. Vibration is minimized. Seating comfort is ensured. Accessibility features are provided. Information systems are clear. Emergency procedures are posted. Security measures are comprehensive. Access control is implemented. Surveillance systems monitor areas. Intrusion detection is automatic. Threat assessment is continuous. Emergency response is coordinated. Staff training is regular. Security audits are conducted. Incident reporting is mandatory. Recovery procedures are tested. Technical Specifications\nCommunication protocols are standardized. Data formats are defined. Interface specifications are documented. Interoperability is ensured. Version control is maintained. Change management is formal. Configuration control is strict. Testing procedures are comprehensive. Validation methods are defined. Verification processes are documented. Hardware specifications are detailed. Environmental ratings are specified. Electromagnetic compatibility is ensured. Mechanical requirements are defined. Electrical parameters are documented. Thermal management is designed. Vibration resistance is tested. Shock tolerance is verified. Corrosion protection is applied. Weatherproofing is implemented. Software requirements are comprehensive. Real-time performance is guaranteed. Memory usage is optimized. Processing capacity is adequate. Response times are specified. Error handling is robust."}
{"id":8,"text":"Response times are specified. Error handling is robust. Recovery procedures are automatic. Logging capabilities are extensive. Debugging tools are provided. Update mechanisms are secure. Maintenance Requirements\nPreventive maintenance schedules are established. Corrective maintenance procedures are defined. Emergency repair capabilities are maintained. Spare parts inventory is managed. Tool requirements are specified. Personnel qualifications are defined. Training programs are implemented. Safety procedures are enforced. Documentation standards are maintained. Quality control is implemented. Inspection intervals are defined. Testing frequencies are specified. Calibration schedules are maintained. Replacement criteria are established. Upgrade procedures are documented. Modification controls are implemented. Change approval processes are formal. Impact assessments are required. Risk evaluations are conducted. Safety analyses are updated. Maintenance records are comprehensive. Work order systems track activities. Performance data is collected. Trend analysis identifies issues. Cost tracking enables optimization. Resource planning ensures availability. Scheduling systems coordinate activities. Progress monitoring ensures completion. Quality assurance verifies work. Customer satisfaction is measured."}
{"id":9,"text":"ERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 1/25 \n \n \n \n \n \n \nERTMS/ATO \nATO-OB / ETCS-OB FFFIS \nApplication Layer \nREF  :  SUBSET-130 \nISSUE :  1.0.0 \nDATE : \n05-07-23 \n \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 2/25 \n \n1. MODIFICATION HISTORY \nIssue Number \nDate \nSection \nNumber \nModification / Description Author \n0.0.1 \n12-11-2014 \nAll \nNew document \nUNISIG ATO WP \n0.0.2 \n14-11-2014 \nAll \nThe document content has been changed \naccording to the UNISIG ATO WP \ncomments referring to the last version \n(0.0.1). UNISIG ATO WP \n0.0.3 \n20-02-2015 \nAll \nThe document content has been changed \naccording to the EUG and UNISIG ATO \nWP comments referring to the last version \n(0.0.2). UNISIG ATO WP \n0.0.4 \n14-09-2016 \nAll \nNew \ndocument \nstructure \nand \ninteroperability level. UNISIG ATO WP \n0.0.5 \n25-10-2016 \nAll \nThe document content has been updated \naccording to UNISIG ATO WP review. UNISIG ATO WP \n0.0.6 \n19-12-2016 \nAll \nThe document content has been updated \naccording to UNISIG ATO WP review. UNISIG ATO WP \n0.0.7 \n22-12-2016 \nAll \nThe document content has been updated \naccording to UNISIG ATO WP review. UNISIG ATO WP \n0.0.8 \n02-05-2017 \nAll \nThe document content has been updated \naccording to UNISIG SG, EUG and ATO \nWP review. UNISIG ATO WP \n0.0.9 \n19-05-2017 \nAll \nThe document content has been updated \naccording to ATO WP review. UNISIG ATO WP \n0.0.10 draft A \n18-01-2018 \nAll \nThe document content has been updated \naccording to ERA and UNISIG SG review. UNISIG ATO WP \n0.0.10 \n15-02-2018 \nAll \nThe document content has been updated \naccording to ERA and UNISIG SG review. UNISIG ATO WP \n0.0.11 \n09-03-2018 \nAll \nThe document content has been updated \nto be aligned with SUBSET-125-0018 and \nincluding Specific ATO Data packets. UNISIG ATO WP \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 3/25 \n \nIssue Number \nDate \nSection \nNumber \nModification / Description Author \n0.0.12 draftA \n16-04-2018 \nAll \nThe document content has been updated \nto be aligned with SUBSET-125-0019 and \nERA and UNISIG SG comments on \nversion 0011. UNISIG ATO WP \n0.1.0 \n04-05-2018 \nAll \nThe document content has been updated \nto be aligned with SUBSET-125-010 and \nERA \nand \nUNISIG \nSG \nremaining \ncomments on version 0011. UNISIG ATO WP \n0.1.1 \n12-11-2020 \nAll \nImplement changes according to EECT \n57-68 \nUNISIG ATO WP \n0.1.2 \n21-06-2021 \n6.2 \n******* \n1 \nConsolidation Phase \n \n- Add timeout values to list of packets \n \n- Add M_MODE & remove Q_ADMode \nRemove column author (CR S125-192 – \nEECT#70 Subset 139 review id 5) \nUNISIG ATO WP \n0.1.3 \n25-06-2021 \n******* \n5.2 \nM_MODE variable shifted to supervision \ninformation \nRemove column author from section 5.2 \nUNISIG ATO WP \n0.1.4 \n02-07-2021 \n*******. Remove “Q_DIRCONTROLLER” as it is \nshifted to SS-139 \nUNISIG ATO WP \n0.1.5 \n15-07-2021 \n7.2.2.1/******* \n \n \n5.2 \n“Q_DIRCONTROLLER” properly removed \nchanges \non \nD_ANTENNA \ndue \nto \nEECT#066 \n \nRestore column author \nUNISIG ATO WP \n0.1.6 \n27-08-2021 \n******* \n \n1 \n5.2 \nNID_ACTIVE_ANTENNA_LRBG moved \nby one item offset."}
{"id":10,"text":"UNISIG ATO WP \n0.1.1 \n12-11-2020 \nAll \nImplement changes according to EECT \n57-68 \nUNISIG ATO WP \n0.1.2 \n21-06-2021 \n6.2 \n******* \n1 \nConsolidation Phase \n \n- Add timeout values to list of packets \n \n- Add M_MODE & remove Q_ADMode \nRemove column author (CR S125-192 – \nEECT#70 Subset 139 review id 5) \nUNISIG ATO WP \n0.1.3 \n25-06-2021 \n******* \n5.2 \nM_MODE variable shifted to supervision \ninformation \nRemove column author from section 5.2 \nUNISIG ATO WP \n0.1.4 \n02-07-2021 \n*******. Remove “Q_DIRCONTROLLER” as it is \nshifted to SS-139 \nUNISIG ATO WP \n0.1.5 \n15-07-2021 \n7.2.2.1/******* \n \n \n5.2 \n“Q_DIRCONTROLLER” properly removed \nchanges \non \nD_ANTENNA \ndue \nto \nEECT#066 \n \nRestore column author \nUNISIG ATO WP \n0.1.6 \n27-08-2021 \n******* \n \n1 \n5.2 \nNID_ACTIVE_ANTENNA_LRBG moved \nby one item offset. Restore column author \nRemove column author \nUNISIG ATO WP \n0.1.7 \n15-09-2021 \nAll \nclean up tracked changes \nUINISG ATO WP \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 4/25 \n \nIssue Number \nDate \nSection \nNumber \nModification / Description Author \n0.1.8 \n02-11-2021 \n7.2.1.2 \n7.2.2.1 \nRemarks from EECT#78 (T_DWELLTIME \nand active antenna topic) \nUNISIG ATO WP \n0.1.9 \n20.01.2022 \n******* \nCR #(SS-130-)01 from EECT#81 \nUNISIG ATO WP \n0.1.10 \n27.01.2022 \nAll \nType: “ATO over ETCS” changed to \n“ERTMS/ATO” \nUNISIG ATO WP \n0.2.0 \n16.03.2022 \nNone \n \nUNISIG ATO WP \n0.2.1 \n18.01.2023 \n6.2.2.1, \n6.2.2.2, ******* Comments from EECT#91 \nUNISIG ATO WP \n0.2.2 \n21.02.2023 \n \nChanges due to CR1344 & CR1370 \nRemoval of watermark \nUNISIG ATO WP \n0.2.3 \n24.03.2023 \n1, \n6.2.2 \nFormal remarks from EECT#94 \nUpdate Dates in modification history \nMove ******* back to 6.2.2 (mistake when \ncreating v0.2.2) \nUNISIG ATO WP \n1.0.0 \n05.07.2023 \nNone \nVersion for ERTMS/ATO Baseline 1 1st \nrelease and ERTMS/ETCS Baseline 4 1st \nrelease \nUNSIG ATO WP \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 5/25 \n \n2. \nTABLE OF CONTENTS \n1. MODIFICATION HISTORY ............................................................................................................... 2 \n2. TABLE OF CONTENTS .................................................................................................................... 5 \n3. LIST OF TABLES ............................................................................................................................ 6 \n4. INTRODUCTION ............................................................................................................................. 7 \n \nScope and purpose of the document ................................................................................. 7 \n \nReference documents ....................................................................................................... 7 \n \nAbbreviations .................................................................................................................... 8 \n \nDefinitions ......................................................................................................................... 8 \n5. PRINCIPLES.................................................................................................................................. 9 \n \nDefinition of the Variables ................................................................................................. 9 \n \nDefinition of the Packets ................................................................................................... 9 \n6. PACKET DESCRIPTION ................................................................................................................ 10 \n \nList of Packets ................................................................................................................. 10 \n \nUser Data ........................................................................................................................ 11 \n6.2.1 \nPackets: ATO-OB to ETCS-OB ................................................................................. 11 \n6.2.2 \nPackets: ETCS-OB to ATO-OB ................................................................................. 15 \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 6/25 \n \n3. \nLIST OF TABLES \nTable 1 Reference Documents ........................................................................................................ 7 \nTable 2 Packet summary ............................................................................................................... 10 \nTable 3 Packet Number 0: ATO_ETCS_Status ............................................................................. 11 \nTable 4 Packet Number 1: ATO_ETCS_DMI ................................................................................. 12 \nTable 5 Packet Number 2: ATO_ETCS_Data_Entry_Need ........................................................... 13 \nTable 6 Packet Number 3: ATO_ETCS_Data_Entry_Request ...................................................... 13 \nTable 7 Packet Number 4: ATO_ETCS_Data_View_Values ......................................................... 14 \nTable 8 Packet Number 5: ETCS_ATO_Static .............................................................................. 16 \nTable 9 Packet Number 6: ETCS_ATO_Dynamic ......................................................................... 22 \nTable 10 Packet Number 7: ETCS_ATO_Driver_Inputs ................................................................ 22 \nTable 11 Packet Number 8: ETCS_ATO_Data_Entry_Values ....................................................... 23 \nTable 12 Packet Number 9: ETCS_ATO_Data_Entry_Flag ..........................................................."}
{"id":11,"text":"16 \nTable 9 Packet Number 6: ETCS_ATO_Dynamic ......................................................................... 22 \nTable 10 Packet Number 7: ETCS_ATO_Driver_Inputs ................................................................ 22 \nTable 11 Packet Number 8: ETCS_ATO_Data_Entry_Values ....................................................... 23 \nTable 12 Packet Number 9: ETCS_ATO_Data_Entry_Flag ........................................................... 24 \nTable 13 Packet Number 11: ETCS_ATO_BRAKE_DECELERATIONS........................................ 25 \n \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 7/25 \n \n4. INTRODUCTION \n \nScope and purpose of the document \n \nThe purpose of this document is to define the Application Layer of the ATO-OB / ETCS-\nOB FFFIS to support ERTMS/ATO. The lower layers of communication details are \nspecified in [Ref 10]. The scope of this document is the definition of the standardised set of data to transmit \nbetween the ATO-OB and the ETCS-OB to support ERTMS/ATO. The requirements associated to this interface are included in [Ref 1] (ERTMS/ATO \nSystem Requirement Specification SUBSET-125) and [Ref 4] (System Requirement \nSpecification SUBSET-026). Reference documents \nRef. N° Title \nReference [Ref 1] \nERTMS/ATO System Requirements \nSpecification \nSUBSET-125 [Ref 2] \nATO-OB / ATO-TS FFFIS Application \nLayer \nSUBSET-126 [Ref 3] \nERTMS/ATO Glossary \n13E154 [Ref 4] \nERTMS/ETCS System Requirements \nSpecification \nSUBSET-026 [Ref 5] \nFIS Juridical Recording \nSUBSET-027 [Ref 6] \nDimensioning and Engineering rules \nSUBSET-040 [Ref 7] \nIEEE 802.3 Ethernet Standard \nNA [Ref 8] \nFFFIS STM Application Layer \nSUBSET-058 [Ref 9] \nGlossary of Terms and Abbreviations \nSUBSET-023 [Ref 10] Interface Specification \nCommunication Layers for On-board \nCommunication \nSUBSET-143 \nTable 1 Reference Documents \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 8/25 \n \n \nAbbreviations \n \nFor ATO related abbreviations see ERTMS/ATO Glossary [Ref 3]. For ETCS related abbreviations see SUBSET-023 [Ref 9]. Definitions \n \nFor ATO related definitions see ERTMS/ATO Glossary [Ref 3]. For ETCS related definitions see SUBSET-023 [Ref 9]. ERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 9/25 \n \n5. PRINCIPLES \n \nDefinition of the Variables \n \nAll variables defined within this document shall comply with [Ref 10]. The variables already defined in ETCS are used in this interface by using the same name \nand giving the corresponding reference in its description and resolution/formula. In the case that the length of an ETCS variable is not aligned with any of the data types \ndefined in [Ref 10] and is not part of a BITSET, the needed amount of “0” values will be \nadded in the most significant bits of the variable, to align it with the data type defined in \nthe corresponding column of the packet description table. Definition of the Packets \n \nThe following chapters will describe the user data only. All details on the way how User \nData are transformed to packets can be found in [Ref 10]. Packets are multiple variables \ngrouped into a single unit, with a defined internal structure. ERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 10/25 \n \n6. PACKET DESCRIPTION \n \nList of Packets \nPacket \nNumber \nPacket Name \nSource \nSink \nTransmitting \ncycle [ms] \nData Class [Ref 10]"}
{"id":12,"text":"[ms] \nData Class [Ref 10] Timeout [ms] \n0 \nATO_ETCS_Status \nATO \nETCS \n100 \nProcess Data \n1000 \n1 \nATO_ETCS_DMI \nATO \nETCS \n100 \nProcess Data \n1000 \n2 \nATO_ETCS_Data_Entry_Need \nATO \nETCS \nNA \nMessage Data \n- \n3 \nATO_ETCS_Data_Entry_Request \nATO \nETCS \nNA \nMessage Data \n- \n4 \nATO_ETCS_Data_View_Values \nATO \nETCS \nNA \nMessage Data \n- \n5 \nETCS_ATO_Static \nETCS \nATO \n1000 \nProcess Data \n3000 \n6 \nETCS_ATO_Dynamic \nETCS \nATO \n200 \nProcess Data \n1000 \n7 \nETCS_ATO_Driver_Inputs \nETCS \nATO \n100..200 \nProcess Data \n1000 \n8 \nETCS_ATO_Data_Entry_Values \nETCS \nATO \nNA \nMessage Data \n- \n9 \nETCS_ATO_Data_Entry_Flag \nETCS \nATO \nNA \nMessage Data \n- \n10 \nETCS_ATO_Data_View_Values_\nRequest \nETCS \nATO \nNA \nMessage Data \n- \n11 \nETCS_ATO_BRAKE_DECELERA\nTIONS \nETCS \nATO \n200 \nProcess Data \n1000 \nTable 2 Packet summary \n \nThe packets for which no transmitting cycle is defined in Table 2 are sent event-based. The packet numbers defined in Table 2 correspond to NID_PACKET definition given in \n[Ref 10]. This interface uses Slot 1 (see [Ref 10]). ERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 11/25 \n \n \nUser Data \n6.2.1 \nPackets: ATO-OB to ETCS-OB \n \nPacket Number 0: ATO_ETCS_Status \nPacket Number \n0 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n \nBit \nATO_INFO_SET \nATO information BITSET \nBITSET8 \n \n1 \n0 \nQ_AD_MODE_REQUEST \nQualifier to request the ETCS AD \nMode. Values: \n0 = AD Mode not requested \n1 = AD Mode requested \n2 \n1..7 \nSpare \n \n \nTable 3 Packet Number 0: ATO_ETCS_Status \n \nPacket Number 1: ATO_ETCS_DMI \nPacket Number \n1 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n \nBit \nATO_DMI_INFO \nDMI Indicators BITSET \nBITSET16 \n \n1 \n0..2 \nM_ATOSTATUS \nATO status to be displayed. Values: \n0 = ATO Selected \n1 = ATO ready for engagement \n2 = ATO Engaged \n3 = ATO Disengaging \n4 = ATO Failure \n5 - 7 = spare \n2 \n3..4 \nQ_STOPACCURACY \nStopping accuracy information to \nbe displayed. Values: \n0 = No stopping accuracy indication \n1 = Accurate stop \n2 = Undershoot \n3 = Overshoot \n3 \n5..6 \nQ_DWELLTIME_INFO \nDwell Time information to be \ndisplayed. Values: \n0 = No Dwell Time indication \n1 = Remaining Dwell Time \n2 = Train Hold \n3 = Spare \n4 \n7..9 \nQ_DOORINFO \nTrain door information to be \ndisplayed. Values: \n0 = No information  \n1 = Request driver to close doors  \n2 = Request driver to open doors on \nboth sides \n3 = Request driver to open right \ndoors \n4 = Request driver to open left doors \n5 = Doors are open  \n6 = Doors are closed \n7 = Doors are being closed by ATO  \n5 \n10..11 \nQ_SKIPSTP \nSkip Stopping Point information to \nbe displayed. Values: \n0 = No information \n1 = Skip Stopping Point requested \nby the driver \n2 = Skip Stopping Point requested \nby the ATO-TS \n3 = Skip Stopping Point Inactive \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 12/25 \n \nPacket Number \n1 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n6 \n12 \nQ_COASTING \nCoasting indication to be displayed. Values: \n0"}
{"id":13,"text":"UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 12/25 \n \nPacket Number \n1 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n6 \n12 \nQ_COASTING \nCoasting indication to be displayed. Values: \n0 = No information \n1 = Coasting advice \n7 \n13 \nQ_WARNINGSOUND \nQualifier indicating if the ATO-OB is \nrequesting \nthe \nETCS-OB \nto \nproduce a warning sound. Values: \n0 = Warning sound not requested \n1 = Warning sound requested \n8 \n14..15 \nSpare \n \n \n9 \nT_DWELLTIME \nRemaining Dwell Time to be \ndisplayed. UINT16 Resolution: 1 s \nSpecial value:  \n65535 = no remaining Dwell Time \nindication \n10 \nV_TAS \nTarget \nAdvice \nSpeed \nto \nbe \ndisplayed. UINT16 Resolution: 1 cm/s \nSpecial values:  \n16668 - 65534 = spare \n65535 = No Target Advice Speed \nindication \n11 \nD_NEXTADVICE \nDistance to next advice change to \nbe displayed. UINT32 \nResolution: 1 cm \nSpecial value:  \n(232-1) = No information \n12 \nT_NEXT_STP_ARRIVAL_TIME \nArrival time to the next Stopping \nPoint or Stopping Points to be \nskipped to be displayed It is the \nnumber of seconds from the \nreference time 00:00:00 in local \ntime. UINT32 \nResolution: 1 s \nSpecial value:  \n86400 … interpreted by ECTS-OB \n(DMI) as “24:00:00” \n86401 - (232-2) = spare \n(232-1) = No information \n13 \nL_TEXT_STP \nLength of text string in bytes for the \nname of the next Stopping Point or \nStopping Points to be skipped. UINT8 \nSpecial values:    \n0 = No information to display \n(L_TEXT_STP) - times \n14 \nX_TEXT_STP (k) \nName of the next Stopping Point or \nStopping Points to be skipped to be \ndisplayed. UINT8 \nSee [Ref 8], Section §8.1.120. \n15 \nN_STPDISTANCE_ITER \nNumber of “distance to the next \nStopping Point or Stopping Point to \nbe skipped” elements. UINT8 \nSpecial values:    \n0 = No information to display \n(N_STPDISTANCE_ITER) - times \n16 \nD_STPDISTANCE (l) Distance to the Stopping Point or \nStopping Point to be skipped to be \ndisplayed. UINT32 \nResolution: 1 cm \nTable 4 Packet Number 1: ATO_ETCS_DMI \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 13/25 \n \n \nPacket Number 2: ATO_ETCS_Data_Entry_Need \nPacket Number \n2 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n \nBit \nATO_Data_Entry_Need \n \nBITSET8 \n \n1 \n0 \nQ_ATO_DATAENTRY \nQualifier indicating if the ATO-OB \nneeds Specific ATO Data or not. Values: \n0 = No Specific ATO Data needed. 1 = Specific ATO Data needed. 2 \n1..7 \nSpare \n \n \nTable 5 Packet Number 2: ATO_ETCS_Data_Entry_Need \n \nPacket Number 3: ATO_ETCS_Data_Entry_Request \nPacket Number \n3 \n  \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n1 \nN_DER_ITER \nNumber of Specific ATO Data requested. UINT8 \nSpecial values:  \n0 = “End of Specific ATO Data Entry” \n16 - 255 = spare \n(N_DER_ITER) - times \n2 \nNID_DATA_ATO (k) Identifier of the Specific ATO Data. UINT8 \nNumbers \n3 \nL_CAPTION (k) \nSee [Ref 8] §8.1.8 \nUINT8 \nSee [Ref 8] §8.1.8 \n(L_CAPTION (k)) - times \n4 \nX_CAPTION (k, l) See [Ref 8] §8.1.119 \nUINT8 \nSee [Ref 8] §8.1.119 \n5 \nL_VALUE (k) See [Ref 8] §8.1.12 \nUINT8 \nSee [Ref 8] §8.1.12 \n(L_VALUE (k)) - times \n6 \nX_VALUE (k, m) See [Ref 8] §8.1.121 \nUINT8 \nSee [Ref 8]"}
{"id":14,"text":"[Ref 8] §8.1.121 \nUINT8 \nSee [Ref 8] §8.1.121 \n7 \nN_DKV_ITER (k) \nNumber of dedicated keyboard values. UINT8 \nSpecial values:  \n0 = there is no dedicated keyboard \n(N_DKV_ITER) - times \n8 \nL_VALUE (k, n) See [Ref 8] §8.1.12 \nUINT8 \nSee [Ref 8] §8.1.12 \n(L_VALUE (k,n)) - times \n9 \nX_VALUE (k, n, o) See [Ref 8] §8.1.121 \nUINT8 \nSee [Ref 8] §8.1.121 \nTable 6 Packet Number 3: ATO_ETCS_Data_Entry_Request \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 14/25 \n \n \nPacket Number 4: ATO_ETCS_Data_View_Values \nPacket Number \n4 \n  \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n1 \nN_DVV_ITER \nNumber of Data View values. UINT8 \nSpecial values:  \n \n0 = “No Specific ATO Data values” \n16 - 255 = spare \n(N_DVV_ITER) - times \n2 \nNID_DATA_ATO (k) Identifier of the Specific ATO Data. UINT8 \nNumbers \n3 \nL_CAPTION (k) \nSee [Ref 8] §8.1.8 \nUINT8 \nSee [Ref 8] §8.1.8 \n(L_CAPTION (k)) - times \n4 \nX_CAPTION (k, l) See [Ref 8] §8.1.119 \nUINT8 \nSee [Ref 8] §8.1.119 \n5 \nL_VALUE (k) See [Ref 8] §8.1.12 \nUINT8 \nSee [Ref 8] §8.1.12 \n(L_VALUE (k)) - times \n6 \nX_VALUE (k, m) See [Ref 8] §8.1.121 \nUINT8 \nSee [Ref 8] §8.1.121 \nTable 7 Packet Number 4: ATO_ETCS_Data_View_Values \n \n \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 15/25 \n \n6.2.2 \nPackets: ETCS-OB to ATO-OB \n \nPacket Number 5: ETCS_ATO_Static \nPacket Number \n5 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n \nBit \nETCS_DATA \nETCS valid data qualifiers set \nBITSET8 \n \n1 \n0 \nQ_TRAIN_DATA_\nVALID \nQualifier indicating if the ETCS Train Data are \nvalid. Values: \n0 = ETCS Train Data not valid \n1 = ETCS Train Data valid \n2 \n1 \nQ_OPERATIONA\nL_DATA_VALID \nQualifier indicating if the ETCS Operational \nData (NID_OPERATIONAL and DRIVER_ID) \nare valid. Values: \n0 = ETCS Operational Data not valid \n1 = ETCS Operational Data valid \n3 \n2..7 \nSpare \n \n \n4 \nNID_ENGINE See [Ref 4] §******** \nUINT32 See [Ref 4] §******** \n5 \nNID_ANTENNA \nIdentification of the antenna. UINT8 \nValues: \n0 = antenna 1 \n1 = antenna 2 \n2 = antenna 3 \n3 = antenna 4 \nSpecial Values: \n4 .. 255 \n6 \nD_ANTENNA \nDistance from antenna to the train front end. UINT16 Resolution: 1 cm \n7 \nN_ANTENNA_ITER \nNumber of additional antennas installed on-\nboard \nUINT8 \nValues: \n0 .. 3 \nSpecial Values: \n4 .. 255 \n(N_ANTENNA_ITER) - times \n8 \n \nNID_ANTENNA(i) Identification of the additional antenna \nUINT8 \nValues: \n0 = antenna 1 \n1 = antenna 2 \n2 = antenna 3 \n3 = antenna 4 \nSpecial Values: \n4 .. 255 \n9 \nD_ANTENNA(i) \nDistance from additional antenna to the train \nfront end. UINT16 Resolution: 1 cm \n10 \nL_TRAIN [If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 4] §7.5.1.56 \nUINT16 See [Ref 4] §7.5.1.56  \n11 \nV_MAXTRAIN [If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 4] §7.5.1.160 \nUINT8 \nSee [Ref 4] §7.5.1.160  \n12 \nNC_CDTRAIN [If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 4] §7.5.1.82.2 \nUINT8 See [Ref 4] §7.5.1.82.2 \n13 \nNC_TRAIN [If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 4] §7.5.1.84 \nBITSET16 See [Ref 4] §7.5.1.84 \n14 \nM_AXLELOADCAT [If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 4] §7.5.1.62 \nUINT8 \nSee [Ref 4]"}
{"id":15,"text":"[If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 4] §7.5.1.62 \nUINT8 \nSee [Ref 4] §7.5.1.62 \n15 \nM_NOM_ROT_MASS [If Q_TRAIN_DATA_VALID = 1] \nSee [Ref 5] §******* \nUINT8 \nSee [Ref 5] §*******  \n16 \nM_BRAKE_PERCENTAGE_\nATO [If Q_TRAIN_DATA_VALID = 1] \nBrake percentage from ETCS Train Data. UINT8 \nResolution: 1 % \nSpecial Values:  \n251 - 254 = spare \n255 = Not relevant for trains on \nwhich the braking models are \ncaptured as Train Data \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 16/25 \n \nPacket Number \n5 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n17 \nM_BRAKE_POSITION_ATO [If Q_TRAIN_DATA_VALID = 1] \nBrake position from ETCS Train Data. UINT8 \nValues: \n0 = Passenger train in P \n1 = Freight train in P \n2 = Freight train in G \nSpecial Values:  \n3 - 255 = spare \n18 \nQ_INDEX_GAMMA_CONF [If Q_TRAIN_DATA_VALID = 1] \nQualifier indicating the set of full service \nbraking models preconfigured in the ETCS-\nOB, which are currently applicable according \nto the capture of the ETCS Train Data. UINT8 \nSpecial Values:  \n255 = Not relevant for trains on \nwhich the brake percentage is \nacquired as part of Train Data and \nthe conversion model is applicable \n19 \nNID_OPERATIONAL [If Q_OPERATIONAL_DATA_VALID = 1] \nSee [Ref 4] §******** \nBCD32 \nSee [Ref 4] §********  \n20 \nDRIVER_ID [If Q_OPERATIONAL_DATA_VALID = 1] \nSee [Ref 5] §******* and [Ref 4] §A.3.11 \nSTRING16 See [Ref 5] §******* \nSee [Ref 4] §A.3.11 \nTable 8 Packet Number 5: ETCS_ATO_Static \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 17/25 \n \n \nPacket Number 6: ETCS_ATO_Dynamic \nPacket Number \n6 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n \nBit \nETCS_ATO_INFO_SET \nETCS Information BITSET \nBITSET8 \n \n \n1 \n0 \nM_ADHESION_DRIVER \nAdhesion factor set by the driver. Values: \n0 = slippery rail is set by the \ndriver \n1 = slippery rail is not set by \nthe driver \n2 \n1 \nQ_APPCOND \nQualifier \nindicating \nif \nthe \nETCS \napplicable \nconditions \nfor \nATO \nOperational are fulfilled. Values: \n0 = ETCS applicable \nconditions for ATO \nOperational are not fulfilled \n1 = ETCS applicable \nconditions for ATO \nOperational are fulfilled \n3 \n2..3 \nQ_RC \nFactor to indicate whether a movement \nin the direction of the train orientation \ncorresponds to an increase or a \ndecrease of the position counter. Values: \n0 = unknown (no train \norientation) \n1 = Increase (factor = 1) \n2 = Decrease (factor = -1) \n4 \n4..7 \nSpare \n \n \nPositioning Information \n \nBit \nPOSITION_REPORT_SET \nPosition Report BITSET \nBITSET8 \n \n5 \n0..1 \nQ_DIRSOLR See [Ref 5] §******* \nSee [Ref 5] §******* \n6 \n2..3 \nQ_DSOLR \nSee [Ref 5] §******* \nSee [Ref 5] §******* \n7 \n4..7 \nSpare \n \n \n8 \nN_LOC_REF \nValue of the position counter at the \nmoment the data of the packet is \ndetermined. INT32 \nResolution: 1 cm  \nSpecial value:  \n(231-1) = spare \nNote: the “spare” value is used \nas a special value in other \nvariables which depend on this \ncounter (e.g. \nN_LOC_REFBALISE). 9 \nT_LOC_REF \nTime at which the position counter is \ndetermined."}
{"id":16,"text":"INT32 \nResolution: 1 cm  \nSpecial value:  \n(231-1) = spare \nNote: the “spare” value is used \nas a special value in other \nvariables which depend on this \ncounter (e.g. \nN_LOC_REFBALISE). 9 \nT_LOC_REF \nTime at which the position counter is \ndetermined. UINT32 \nResolution: 1 ms \nSpecial value:  \n(232-1) = unknown \n10 \nN_LOC_REFBALISE \nValue of the position counter at the \ncenter of balise used as location \nreference by the ETCS on-board, i.e. \nthe reference balise of the ETCS SOLR \nor the balise duplicating this one, see \n[Ref 4] §********.3. INT32 \nResolution: 1 cm \nSpecial value:  \n(231-1) = unknown \n11 \nNID_ACTIVE_ANTENNA_SOLR \nIdentification of the antenna active \nwhen the reference balise of the ETCS \nSOLR was passed \nUINT8 \nValues: \n0 = antenna 1 \n1 = antenna 2 \n2 = antenna 3 \n3 = antenna 4 \nSpecial Values:  \n4 - 255 = spare \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 18/25 \n \nPacket Number \n6 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n12 \nNID_REFBALISE \nIdentification of the balise used as \nlocation reference by the ETCS on-\nboard, i.e. the reference balise of the \nETCS SOLR or the balise duplicating \nthis one, see [Ref 4]  §********.3. BITSET32 \nValues: \nBit00 to bit02 = N_PIG as \ndefined in [Ref 4] § ******** \nBit03 to bit16 = NID_BG as \ndefined in [Ref 4] § ******** \nBit17 to bit26 = NID_C as \ndefined in [Ref 4] § ******** \nBit27 to bit31 = not relevant \nSpecial values: \n(232-1) = unknown \n13 \nT_LOC_REFBALISE \nTime at which the position counter was \nequal to N_LOC_REFBALISE. UINT32 \nResolution: 1 ms \nSpecial value:  \n(232-1) = unknown \n14 \nN_LOC_BALISERUNOVER1 \nValue of the position counter at the last \nbalise passed. INT32 \nResolution: 1 cm \nSpecial value:  \n(231-1) = unknown \n15 \nNID_ACTIVE_ANTENNA_BRO1 \nIdentification of the antenna active \nwhen the last balise was passed \nUINT8 \nValues: \n0 = antenna 1 \n1 = antenna 2 \n2 = antenna 3 \n3 = antenna 4 \nSpecial Values:  \n4 - 255 = spare \n16 \nNID_BALISERUNOVER1 \nIdentification of the last balise passed. BITSET32 \nValues: \nBit00 to bit02 = N_PIG as \ndefined in [Ref 4] § ******** \nBit03 to bit16 = NID_BG as \ndefined in [Ref 4] § ******** \nBit17 to bit26 = NID_C as \ndefined in [Ref 4] § ******** \nBit27 to bit31 = not relevant \nSpecial values: \n(232-1) = unknown \n17 \nT_LOC_BALISERUNOVER1 \nTime at which the position counter was \nequal to N_LOC_BALISERUNOVER1. UINT32 \nResolution: 1 ms \nSpecial value:  \n(232-1) = unknown \n18 \nN_LOC_BALISERUNOVER2 \nValue of the position counter at the \nbalise \npassed \nbefore \nNID_BALISERUNOVER1. INT32 \nResolution: 1 cm \nSpecial value:  \n(231-1) = unknown \n19 \nNID_ACTIVE_ANTENNA_BRO2 \nIdentification of the antenna active \nwhen \nthe \nlast \nbalise \nbefore \nNID_BALISERUNOVER1 was passed \nUINT8 \nValues: \n0 = antenna 1 \n1 = antenna 2 \n2 = antenna 3 \n3 = antenna 4 \nSpecial Values:  \n4 - 255 = spare \n20 \nNID_BALISERUNOVER2 \nIdentification of the balise passed \nbefore NID_BALISERUNOVER1."}
{"id":17,"text":"Resolution: 1 ms \nSpecial value:  \n(232-1) = unknown \n18 \nN_LOC_BALISERUNOVER2 \nValue of the position counter at the \nbalise \npassed \nbefore \nNID_BALISERUNOVER1. INT32 \nResolution: 1 cm \nSpecial value:  \n(231-1) = unknown \n19 \nNID_ACTIVE_ANTENNA_BRO2 \nIdentification of the antenna active \nwhen \nthe \nlast \nbalise \nbefore \nNID_BALISERUNOVER1 was passed \nUINT8 \nValues: \n0 = antenna 1 \n1 = antenna 2 \n2 = antenna 3 \n3 = antenna 4 \nSpecial Values:  \n4 - 255 = spare \n20 \nNID_BALISERUNOVER2 \nIdentification of the balise passed \nbefore NID_BALISERUNOVER1. BITSET32 \nValues: \nBit00 to bit02 = N_PIG as \ndefined in [Ref 4] § ******** \nBit03 to bit16 = NID_BG as \ndefined in [Ref 4] § ******** \nBit17 to bit26 = NID_C as \ndefined in [Ref 4] § ******** \nBit27 to bit31 = not relevant \nSpecial values: \n(232-1) = unknown \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 19/25 \n \nPacket Number \n6 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n21 \nT_LOC_BALISERUNOVER2 \nTime at which the position counter was \nequal to N_LOC_BALISERUNOVER2. UINT32 \nResolution: 1 ms \nSpecial value:  \n(232-1) = unknown \n22 \nL_UNCERTAINTY_OVERREADING \nOver-reading amount of the confidence \ninterval to the train position referred to \nthe SOLR. UINT32 Resolution: 1 cm  \nSpecial values: \n(232-1) = unknown \n23 \nL_UNCERTAINTY_UNDERREADIN\nG \nUnder-reading \namount \nof \nthe \nconfidence interval to the train position \nreferred to the SOLR. UINT32 \nResolution: 1 cm  \nSpecial values: \n(232-1) = unknown \nSupervision Information \n24 \nM_MODE \nSee [Ref 4] §******** \nUINT8 \nSee [Ref 4] §******** \n25 \nN_LOC_EBI [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the closest Emergency Brake \nsupervision limit for the current speed \nof the train. INT32 \nResolution: 1 cm  \nSpecial value:   \n(231-1) = No EBI supervised by \nthe ETCS-OB. \n26 \nA_GRADIENT [If M_MODE is equal to FS, AD or OS] \nApplicable \nvalue \nof \nacceleration/deceleration due to the \ngradient from the maximum safe front \nend of the train. INT16 \nResolution: 1 mm/s2 \nValues: \n-2500 … +2500 = Acceleration \n(negative, declining section) \n/Deceleration (positive, \ninclining section) due to \ngradient \nSpecial Values: \n-32768 …  -2501 = spare \n2501 … 32766 = spare \n32767 = unknown \n27 \nN_GRAD_ITER [If M_MODE is equal to FS, AD or OS] \nNumber of gradient changes. UINT8 \nSpecial values:    \n0 = no gradient information \navailable \n52 - 255 = spare \n(N_GRAD_ITER) - times \n28 \nN_LOC_GRADCHANGE (k) [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the A_GRADIENT change. INT32 \nResolution: 1 cm \nSpecial value:   \n(231-1) = not allowed \n29 \nA_GRADIENT (k) [If M_MODE is equal to FS, AD or OS] \nApplicable \nvalue \nof \nacceleration/deceleration due to the \ngradient from the estimated value of the \nposition counter at the A_GRADIENT \nchange. INT16 \nResolution: 1 mm/s2 \nValues: \n-2500 … +2500 = Acceleration \n(negative, declining section) \n/Deceleration (positive, \ninclining section) due to \ngradient \nSpecial values: \n-32768 …  -2501 = spare \n2501 – 32766 = spare  \n32767 = close the gradient \nprofile \n30 \nA_MAXREDADH"}
{"id":18,"text":"INT16 \nResolution: 1 mm/s2 \nValues: \n-2500 … +2500 = Acceleration \n(negative, declining section) \n/Deceleration (positive, \ninclining section) due to \ngradient \nSpecial values: \n-32768 …  -2501 = spare \n2501 – 32766 = spare  \n32767 = close the gradient \nprofile \n30 \nA_MAXREDADH [If M_MODE is equal to FS, AD or OS] \nMaximum deceleration due to reduced \nadhesion conditions from the maximum \nsafe front end of the train. UINT16 \nResolution: 1 mm/s2 \nSpecial value: \n65535 = no maximum \ndeceleration \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 20/25 \n \nPacket Number \n6 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n31 \nN_ADHE_ITER [If M_MODE is equal to FS, AD or OS] \nNumber of A_MAXREDADH changes. UINT8 \nSpecial values: \n0 = no reduced adhesion \nconditions announced \n22 - 255 = spare \n(N_ADHE_ITER) - times \n32 \nN_LOC_ADHCHANGE (l) [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the A_MAXREDADH change. INT32 \nResolution: 1 cm \nSpecial value:   \n(231-1) = not allowed \n33 \nA_MAXREDADH (l) [If M_MODE is equal to FS, AD or OS] \nMaximum deceleration due to reduced \nadhesion conditions from the estimated \nvalue of the position counter at the \nA_MAXREDADH change.. UINT16 Resolution: 1 mm/s2 \nSpecial value: \n65534 = close the adhesion \nprofile \n65535 = no maximum \ndeceleration \n34 \nV_MRSP [If M_MODE is equal to FS, AD or OS] \nSpeed from the minimum safe front end \nof the train. UINT16 \nResolution: 1 cm/s \nSpecial values:  \n16668 - 65534 = spare \n65535 = unknown \n35 \nN_MRSP_ITER [If M_MODE is equal to FS, AD or OS] \nNumber of MRSP iterations. See [Ref 4] §3.13.7.2 \nUINT8 \nSpecial values: \n51 - 255 = spare \n(N_MRSP_ITER) - times \n36 \nN_LOC_MRSP (p) [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the MRSP change including train \nlength compensation. INT32 \nResolution: 1 cm \nSpecial values: \n(231-1) = not allowed \n37 \nV_MRSP (p) [If M_MODE is equal to FS, AD or OS] \nSpeed from the estimated value of the \nposition counter at the MRSP change. UINT16 Resolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = Non numerical value \ntelling that the MRSP ends at \nN_LOC_MRSP(n). 38 \nN_LOC_EOALOA [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat \nthe \nEOA \nor \nLOA \n(including \ntemporary EoAs) currently supervised \nby the ETCS-OB. \nINT32 \nResolution: 1 cm \nSpecial value:  \n(231-1) = no EOA nor LOA \nsupervised by ETCS \n39 \nV_EOALOA [If M_MODE is equal to FS, AD or OS] \nPermitted speed at the EOA or LOA \ncurrently supervised by the ETCS-OB \nUINT16 \nResolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = no EOA nor LOA \nsupervised by ETCS \n40 \nN_LOC_SVL [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the supervised location (SvL). INT32 \nResolution: 1 cm  \nSpecial value:   \n(231-1) = No SvL supervised by \nETCS \n41 \nT_TRACTION"}
{"id":19,"text":"[If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the supervised location (SvL). INT32 \nResolution: 1 cm  \nSpecial value:   \n(231-1) = No SvL supervised by \nETCS \n41 \nT_TRACTION [If M_MODE is equal to FS, AD or OS] \nTime during which the traction effort is \nstill present after the Emergency brake \nintervention. If conversion model is \nused, this variable is to be used only \nwhen braking to standstill. See [Ref 4] \n§********.2.2 \na) \nbut \nwithout \nthe \nexpected brake build up time reduction \nas per A.3.12 of [Ref 4]. UINT16 \nResolution: 10 ms \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 21/25 \n \nPacket Number \n6 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n42 \nT_TRACTION_SPEED [If M_MODE is equal to FS, AD or OS] \nTime during which the traction effort is \nstill present after the Emergency brake \nintervention when braking to a target \nspeed > 0 km/h if conversion model is \nused. See [Ref 4] §********.2.2 a) but \nwithout the expected brake build up \ntime reduction as per A.3.12 of [Ref 4]. UINT16 \nResolution: 10 ms \nSpecial value:  \n65535 = Not relevant in case \nconversion model is not used \n43 \nT_BE_REACT [If M_MODE is equal to FS, AD or OS] \nSafe brake reaction time during which \nthe braking effort is not yet present after \nthe Emergency brake intervention. It is \nthe interval between the command of \nthe brake by the on-board and the \nmoment the brake force starts to build \nup. See [Ref 4] §********.2.3. \nUINT16 Resolution: 10 ms \n44 \nT_BEREM [If M_MODE is equal to FS, AD or OS] Remaining time during which the \ntraction effort is not present until the full \napplication of the braking effort is \nreached. If conversion model is used, \nthis variable is to be used only when \nbraking to standstill. See [Ref 4] \n§********.2.2 b) but without the safe \nbrake build up reduction as per A.3.12 \nof [Ref 4]. UINT16 \nResolution: 10 ms \n45 \nT_BEREM_SPEED [If M_MODE is equal to FS, AD or OS] Remaining time during which the \ntraction effort is not present until the full \napplication of the braking effort is \nreached when braking to a target speed \n> 0 km/h if conversion model is used. See [Ref 4] §********.2.2 b) but without \nthe safe brake build up reduction as per \nA.3.12 of [Ref 4]. UINT16 \nResolution: 10 ms \nSpecial value: \n65535 = Not relevant in case \nconversion model is not used \n46 \nV_PERMITTED [If M_MODE is equal to FS, AD or OS] \nPermitted speed at the current location \n(P). UINT16 Resolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = unknown \n47 \nV_RELEASE_ATO [If M_MODE is equal to FS, AD or OS] \nCurrent release speed of the ETCS-\nOB.  \nUINT16 Resolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = no release speed \navailable \n48 \nN_LOC_RSM"}
{"id":20,"text":"[If M_MODE is equal to FS, AD or OS] \nCurrent release speed of the ETCS-\nOB.  \nUINT16 Resolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = no release speed \navailable \n48 \nN_LOC_RSM [If M_MODE is equal to FS, AD or OS] \nEstimated value of the position counter \nat the RSM start location. INT32 \nResolution: 1 cm \nSpecial value:  \n(231-1) = no release speed \navailable \nSpeed and Acceleration Information \n49 \nV_EST \nCurrent \nestimated \ntrain \nspeed \ncalculated by ETCS Odometry. UINT16 \nResolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = unknown  \n50 \nV_DELTA0 \nCompensation of the inaccuracy of the \nspeed measurement. See [Ref 4] \n§********.2.10. UINT16 \nResolution: 1 cm/s \nSpecial values: \n16668 - 65534 = spare \n65535 = unknown  \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 22/25 \n \nPacket Number \n6 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n51 \nA_EST \nCurrent estimated train acceleration \ncalculated by ETCS-OB. \nINT16 \nResolution: 1 mm/s2 \nValues: \n-5000 … +5000 = Deceleration \n(negative)/Acceleration \n(positive)  \nSpecial Values: \n-32768 …  -5001 = spare \n5001 … 32766 = spare \nSpecial values: \n32767 = unknown \nLinking Information \n52 \nN_LINK_ITER \nNumber of next linked Balise Groups \nannounced. UINT8 \nSpecial values:  \n31 - 255 = spare (See [Ref 6] \n§*******.1 i)). (N_LINK_ITER) - times \n53 \nN_LOC_LINKNBG (q) \nEstimated value of the position counter \nat the linked Balise Group. The position \nis referenced to the balise with the \nN_PIG = 0. INT32 \nResolution: 1 cm \nSpecial values:  \n(231-1) = spare \n54 \nNID_LINKNBG (q) \nIdentification of the linked Balise \nGroup. N_PIG is always 0. BITSET32 \nValues: \nBit00 to bit02 = N_PIG as \ndefined in [Ref 4] § ******** \nBit03 to bit16 = NID_BG as \ndefined in [Ref 4] § ******** \nBit17 to bit26 = NID_C as \ndefined in [Ref 4] § ******** \nBit27 to bit31 = not relevant \nSpecial values: \n(232-1) = unknown \nTable 9 Packet Number 6: ETCS_ATO_Dynamic \n \nPacket Number 7: ETCS_ATO_Driver_Inputs \nPacket Number \n7 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n1 \nN_ATOENGAGE_SELECTION \nATO Engage selection counter. UINT8 \n \n2 \nN_SKIPSTPREQ_SELECTION \nSkip Stopping Point Request selection counter. UINT8 \n \n3 \nN_SKIPSTPREV_SELECTION \nSkip Stopping Point Revocation selection counter. UINT8 \n \nTable 10 Packet Number 7: ETCS_ATO_Driver_Inputs \n \n \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 23/25 \n \n \n \nPacket Number 8: ETCS_ATO_Data_Entry_Values \nPacket Number \n8 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n1 \nN_DEV_ITER \nNumber of data entry values. UINT8 \nSpecial values: \n16 - 255 = spare \n(N_DEV_ITER) - times \n2 \nNID_DATA_ATO (k) \nOne value of this variable represents a Specific \nATO Data required by the ATO-OB. \nUINT8 \nNumbers \n3 \nL_VALUE (k) See [Ref 8] §8.1.12 \nUINT8 \nSee [Ref 8] §8.1.12 \n(L_VALUE (k)) - times \n4 \nX_VALUE (k, l) See [Ref 8] §8.1.121 \nUINT8 \nSee [Ref 8] §8.1.121 \nTable 11 Packet Number 8: ETCS_ATO_Data_Entry_Values \n\nERA * UNISIG *"}
{"id":21,"text":"[Ref 8] §8.1.121 \nUINT8 \nSee [Ref 8] §8.1.121 \nTable 11 Packet Number 8: ETCS_ATO_Data_Entry_Values \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 24/25 \n \n \nPacket Number 9: ETCS_ATO_Data_Entry_Flag \nPacket Number \n9 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n \nBit \nATO_DATA_ENTRY_FLAG \n \nBITSET8 1 \n0 \nM_ATO_DATAENTRYFLAG \nIndicate the beginning or the end of \nthe Specific ATO Data Entry \nprocedure. Values: \n0 = Stop \n1 = Start \n2 \n1..7 \nSpare \n \n \nTable 12 Packet Number 9: ETCS_ATO_Data_Entry_Flag \n \nPacket Number 10: ETCS_ATO_Data_View_Values_Request \n6.******* \nThis packet does not contain user data. Packet Number 11: ETCS_ATO_BRAKE_DECELERATIONS \nPacket Number \n11 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n1 \nN_LOC_REF \nValue of the position counter at the \nmoment the data of the packet is \ndetermined. INT32 \nResolution: 1 cm  \nSpecial value:  \n(231-1) = spare \nNote: the “spare” value is \nused as a special value in \nother variables which \ndepend on this counter \n(e.g. \nN_LOC_REFBALISE). 2 \nT_LOC_REF \nTime at which the position counter of \nthis packet is determined. UINT32 \nResolution: 1 ms \nSpecial value:  \n(232-1) = unknown \n3 \nA_BRAKE_SAFE \nA_BRAKE_SAFE \nvalue \napplicable \nfrom zero speed, related to the brake \nmodel applicable from the maximum \nsafe front end of the train \nUINT16 \nResolution: 1 mm/s2 \nSpecial value: \n65535 = unknown \n4 \nN_BRAKE_SAFE_ITER \nNumber of A_BRAKE_SAFE changes \nrelated to the brake model applicable \nfrom the maximum safe front end of the \ntrain. UINT8 \nSpecial values:    \n0 = no A_BRAKE_SAFE \nchanges available \n10 - 255 = spare \n(N_BRAKE_SAFE_ITER) - times \n5 \nV_CHANGE_BRAKE (m) Value of the speed from which (and \nexcluding) \nthe \nvalue \nof \nA_BRAKE_SAFE (m) is applicable. UINT16 \nResolution: 1 cm/s \nSpecial values:  \n16668 – 65535 = spare \n6 \nA_BRAKE_SAFE (m) Applicable value of A_BRAKE_SAFE. UINT16 Resolution: 1 mm/s2 \n\nERA * UNISIG * EEIG ERTMS USERS GROUP \nSUBSET-130-1.0.0 \nERTMS/ATO – ATO-OB / ETCS-OB FFFIS Application Layer \nPage 25/25 \n \nPacket Number \n11 \n \nItem \nVariable Name \nDescription \nData Type \nResolution/Formula \n7 \nN_SEBDM_ITER \nNumber of location changes of safe \nemergency brake deceleration models \n(e.g. inhibition of special brakes). UINT8 \nSpecial values:    \n0 = no changes in brake \nmodel \n41 - 255 = spare \n(N_SEBDM_ITER) - times \n8 \nN_LOC_SEBDM_CHANGE (n) \nEstimated value of the position counter \nfrom which the safe emergency brake \ndeceleration model is applicable. INT32 \nResolution: 1 cm \nSpecial value:   \n(231-1) = not allowed \n9 \nA_BRAKE_SAFE (n) \nA_BRAKE_SAFE \nvalue \napplicable \nfrom zero speed, related to the brake \nmodel \napplicable \nfrom \nN_LOC_SEBDM_CHANGE (n) \nUINT16 Resolution: 1 mm/s2 \n10 \nN_BRAKE_SAFE_ITER (n) \nNumber of A_BRAKE_SAFE changes \nfor the brake model applicable from \nN_LOC_SEBDM_CHANGE (n). UINT8 \nSpecial values:  \n0 = no A_BRAKE_SAFE \nchanges available \n10 - 255 = spare \n(N_BRAKE_SAFE_ITER) - times \n11 \nV_CHANGE_BRAKE (n, o) Value of the speed from which (and \nexcluding) \nthe \nvalue \nof \nA_BRAKE_SAFE (n,o) is applicable. UINT16 \nResolution: 1 cm/s \nSpecial values:  \n16668 – 65535 = spare \n12 \nA_BRAKE_SAFE (n, o) Applicable value of A_BRAKE_SAFE. UINT16 Resolution: 1 mm/s2 \nTable 13 Packet Number 11: ETCS_ATO_BRAKE_DECELERATIONS"}
