# config.py
import os

# Base directory for the project
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# Data storage paths
DATA_DIR = os.path.join(BASE_DIR, "data")
DB_PATH = os.path.join(DATA_DIR, "requirements.db")
CHROMA_PATH = os.path.join(DATA_DIR, "chroma_db")
BM25_PATH = os.path.join(DATA_DIR, "bm25s_index")

# Ollama and Model Configuration
OLLAMA_HOST = "http://localhost:11434"
EMBEDDING_MODEL = "nomic-embed-text"
GENERATOR_MODEL = "qwen2.5:1.5b"
VALIDATOR_MODEL = "qwen2.5:1.5b"

# Chunking Configuration
CHUNK_SIZE = 500
CHUNK_OVERLAP = 60

# Retrieval Configuration
TOP_K_SEMANTIC = 5
TOP_K_KEYWORD = 5
RRF_WEIGHT_SEMANTIC = 0.6
RRF_WEIGHT_KEYWORD = 0.4

# Create data directory if it doesn't exist
os.makedirs(DATA_DIR, exist_ok=True)