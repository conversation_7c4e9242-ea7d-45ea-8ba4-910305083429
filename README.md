# Customer Requirement Agent - README

This project implements a deterministic Retrieval-Augmented Generation (RAG) system to process customer railway requirements and generate structured technical specifications.

## Prerequisites

1.  **Docker**: Required to run the local LLMs in an isolated environment. Install Docker from [the official website](https://www.docker.com/products/docker-desktop/).
2.  **NVIDIA GPU & Drivers (Recommended)**: For hardware acceleration with Ollama. Ensure you have the latest NVIDIA drivers and the [NVIDIA Container Toolkit](https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/install-guide.html) installed. The application can run on CPU, but it will be significantly slower.
3.  **Python 3.9+**: Required for the application code.

## Step 1: Set Up and Run Ollama with Docker

The project uses a `docker-compose.yml` file to manage the Ollama service.

1.  **Start the Ollama Service**:
    From the project's root directory, run:
    ```bash
    docker-compose up -d
    ```
    This will start the Ollama server in the background.

2.  **Pull the Required LLMs**:
    We will use `llama3` for generation, `qwen` for validation, and `nomic-embed-text` for embeddings. Pull them using the `ollama` command inside the container:
    ```bash
    docker exec -it customer-requirement-agent-ollama-1 ollama pull llama3
    docker exec -it customer-requirement-agent-ollama-1 ollama pull qwen
    docker exec -it customer-requirement-agent-ollama-1 ollama pull nomic-embed-text
    ```
    *Note: `customer-requirement-agent-ollama-1` is the default container name. Verify with `docker ps` if you have issues.*

## Step 2: Set Up the Python Application

1.  **Create a Virtual Environment**:
    ```bash
    python -m venv venv
    source venv/bin/activate  # On Windows, use `venv\Scripts\activate`
    ```

2.  **Install Dependencies**:
    Install all required Python packages from `requirements.txt`.
    ```bash
    pip install -r requirements.txt
    ```

3.  **Download SpaCy Model**:
    The chunker requires a SpaCy model for sentence splitting.
    ```bash
    python -m spacy download en_core_web_sm
    ```

## Step 3: Run the Application

1.  **Start the FastAPI Server**:
    The web service provides the core logic via API endpoints.
    ```bash
    uvicorn webservice.main:app --host 0.0.0.0 --port 8000 --reload
    ```

2.  **Launch the Gradio User Interface**:
    In a **new terminal** (with the virtual environment activated), run the UI.
    ```bash
    python webservice/ui.py
    ```
    This will provide a URL (usually `http://127.0.0.1:7860`). Open this URL in your web browser to use the application.

## How to Use the Interface

1.  **Upload Document**: Use the file upload component to select a customer requirement HTML file.
2.  **Ingest Document**: Click the "Ingest Document" button. This will process the file, create chunks, and build the search indices. You will see a confirmation message.
3.  **Enter Requirement**: Type a specific requirement query (e.g., "The brake system must be failsafe.") into the text box.
4.  **Generate Specification**: Click "Generate Specification". The system will perform hybrid retrieval, generate a structured JSON output, validate it, and display the final result.